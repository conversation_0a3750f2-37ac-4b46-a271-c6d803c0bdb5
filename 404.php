<?php
$page_title = 'Page Not Found';
require_once 'config/database.php';
require_once 'includes/header.php';
?>

<div style="text-align: center; padding: 4rem 0;">
    <div style="max-width: 600px; margin: 0 auto;">
        <i class="fas fa-exclamation-triangle" style="font-size: 6rem; color: var(--warning-color); margin-bottom: 2rem;"></i>
        
        <h1 style="font-size: 3rem; margin-bottom: 1rem;">404</h1>
        <h2 style="color: var(--text-secondary); margin-bottom: 2rem;">Page Not Found</h2>
        
        <p style="font-size: 1.1rem; color: var(--text-muted); margin-bottom: 3rem; line-height: 1.6;">
            The page you are looking for might have been removed, had its name changed, or is temporarily unavailable.
        </p>
        
        <div style="display: flex; gap: 1rem; justify-content: center; flex-wrap: wrap;">
            <a href="/" class="btn btn-primary">
                <i class="fas fa-home"></i> Go Home
            </a>
            
            <?php if (isLoggedIn()): ?>
                <?php if (hasRole('admin')): ?>
                    <a href="/admin/dashboard.php" class="btn btn-outline">
                        <i class="fas fa-tachometer-alt"></i> Dashboard
                    </a>
                <?php elseif (hasRole('police')): ?>
                    <a href="/police/dashboard.php" class="btn btn-outline">
                        <i class="fas fa-tachometer-alt"></i> Dashboard
                    </a>
                <?php elseif (hasRole('citizen')): ?>
                    <a href="/citizen/dashboard.php" class="btn btn-outline">
                        <i class="fas fa-tachometer-alt"></i> Dashboard
                    </a>
                <?php endif; ?>
            <?php else: ?>
                <a href="/auth/login.php" class="btn btn-outline">
                    <i class="fas fa-sign-in-alt"></i> Login
                </a>
            <?php endif; ?>
            
            <a href="/help.php" class="btn btn-secondary">
                <i class="fas fa-question-circle"></i> Help
            </a>
        </div>
    </div>
</div>

<?php require_once 'includes/footer.php'; ?>
