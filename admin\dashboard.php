<?php
$page_title = 'Admin Dashboard';
require_once '../config/database.php';
requireAuth('admin');

try {
    $database = new Database();
    $conn = $database->getConnection();
    
    // Get statistics
    $stats = [];
    
    // Total cases
    $stmt = $conn->query("SELECT COUNT(*) as total FROM criminal_records");
    $stats['total_cases'] = $stmt->fetch()['total'];
    
    // Cases by status
    $stmt = $conn->query("SELECT status, COUNT(*) as count FROM criminal_records GROUP BY status");
    $case_status = [];
    while ($row = $stmt->fetch()) {
        $case_status[$row['status']] = $row['count'];
    }
    
    // Total users by role
    $stmt = $conn->query("SELECT role, COUNT(*) as count FROM users GROUP BY role");
    $user_stats = [];
    while ($row = $stmt->fetch()) {
        $user_stats[$row['role']] = $row['count'];
    }
    
    // Total complaints
    $stmt = $conn->query("SELECT COUNT(*) as total FROM complaints");
    $stats['total_complaints'] = $stmt->fetch()['total'];
    
    // Recent cases
    $stmt = $conn->query("
        SELECT cr.*, u.full_name as created_by_name 
        FROM criminal_records cr 
        LEFT JOIN users u ON cr.created_by = u.id 
        ORDER BY cr.created_at DESC 
        LIMIT 5
    ");
    $recent_cases = $stmt->fetchAll();
    
    // Recent complaints
    $stmt = $conn->query("
        SELECT c.*, u.full_name as citizen_name 
        FROM complaints c 
        LEFT JOIN users u ON c.citizen_id = u.id 
        ORDER BY c.created_at DESC 
        LIMIT 5
    ");
    $recent_complaints = $stmt->fetchAll();
    
} catch (PDOException $e) {
    $error = "Database error: " . $e->getMessage();
}

require_once '../includes/header.php';
?>

<div class="dashboard-header" style="margin-bottom: 2rem;">
    <h1 class="animate-fade-in">
        <i class="fas fa-tachometer-alt"></i>
        Dashboard
    </h1>
</div>

<!-- Statistics Cards -->
<div class="stats-grid" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 1.5rem; margin-bottom: 3rem;">
    
    <!-- Total Cases -->
    <div class="card animate-fade-in" style="background: linear-gradient(135deg, var(--primary-color), #1d4ed8);">
        <div class="card-body" style="color: white;">
            <div style="display: flex; justify-content: space-between; align-items: center;">
                <div>
                    <h3 style="font-size: 2.5rem; margin: 0; font-weight: 700;">
                        <?php echo number_format($stats['total_cases']); ?>
                    </h3>
                    <p style="margin: 0; opacity: 0.9;">Total Cases</p>
                </div>
                <i class="fas fa-folder-open" style="font-size: 3rem; opacity: 0.3;"></i>
            </div>
        </div>
    </div>
    
    <!-- Total Users -->
    <div class="card animate-fade-in" style="background: linear-gradient(135deg, var(--success-color), #059669);">
        <div class="card-body" style="color: white;">
            <div style="display: flex; justify-content: space-between; align-items: center;">
                <div>
                    <h3 style="font-size: 2.5rem; margin: 0; font-weight: 700;">
                        <?php echo number_format(array_sum($user_stats)); ?>
                    </h3>
                    <p style="margin: 0; opacity: 0.9;">Total Users</p>
                </div>
                <i class="fas fa-users" style="font-size: 3rem; opacity: 0.3;"></i>
            </div>
        </div>
    </div>
    
    <!-- Total Complaints -->
    <div class="card animate-fade-in" style="background: linear-gradient(135deg, var(--warning-color), #d97706);">
        <div class="card-body" style="color: white;">
            <div style="display: flex; justify-content: space-between; align-items: center;">
                <div>
                    <h3 style="font-size: 2.5rem; margin: 0; font-weight: 700;">
                        <?php echo number_format($stats['total_complaints']); ?>
                    </h3>
                    <p style="margin: 0; opacity: 0.9;">Total Complaints</p>
                </div>
                <i class="fas fa-exclamation-triangle" style="font-size: 3rem; opacity: 0.3;"></i>
            </div>
        </div>
    </div>
    
    <!-- Resolved Cases -->
    <div class="card animate-fade-in" style="background: linear-gradient(135deg, var(--info-color), #0891b2);">
        <div class="card-body" style="color: white;">
            <div style="display: flex; justify-content: space-between; align-items: center;">
                <div>
                    <h3 style="font-size: 2.5rem; margin: 0; font-weight: 700;">
                        <?php echo number_format($case_status['solved'] ?? 0); ?>
                    </h3>
                    <p style="margin: 0; opacity: 0.9;">Resolved Cases</p>
                </div>
                <i class="fas fa-check-circle" style="font-size: 3rem; opacity: 0.3;"></i>
            </div>
        </div>
    </div>
</div>

<!-- Charts Section -->
<div class="charts-section" style="display: grid; grid-template-columns: 1fr 1fr; gap: 2rem; margin-bottom: 3rem;">
    
    <!-- Case Status Chart -->
    <div class="card animate-fade-in">
        <div class="card-header">
            <h3 class="card-title">
                <i class="fas fa-chart-pie"></i>
                Cases by Status
            </h3>
        </div>
        <div class="card-body">
            <canvas id="caseStatusChart" width="400" height="300"></canvas>
        </div>
    </div>
    
    <!-- User Distribution Chart -->
    <div class="card animate-fade-in">
        <div class="card-header">
            <h3 class="card-title">
                <i class="fas fa-chart-doughnut"></i>
                User Distribution
            </h3>
        </div>
        <div class="card-body">
            <canvas id="userDistributionChart" width="400" height="300"></canvas>
        </div>
    </div>
</div>

<!-- Recent Activity Section -->
<div class="recent-activity" style="display: grid; grid-template-columns: 1fr 1fr; gap: 2rem;">
    
    <!-- Recent Cases -->
    <div class="card animate-fade-in">
        <div class="card-header">
            <h3 class="card-title">
                <i class="fas fa-clock"></i>
                Recent Cases
            </h3>
            <a href="/admin/manage_cases.php" class="btn btn-sm btn-outline">View All</a>
        </div>
        <div class="card-body" style="padding: 0;">
            <?php if (empty($recent_cases)): ?>
                <div style="padding: 2rem; text-align: center; color: var(--text-muted);">
                    <i class="fas fa-inbox" style="font-size: 3rem; margin-bottom: 1rem; opacity: 0.3;"></i>
                    <p>No cases found</p>
                </div>
            <?php else: ?>
                <div class="table-container">
                    <table class="table">
                        <thead>
                            <tr>
                                <th>Case Number</th>
                                <th>Title</th>
                                <th>Status</th>
                                <th>Created</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($recent_cases as $case): ?>
                                <tr>
                                    <td>
                                        <strong><?php echo htmlspecialchars($case['case_number']); ?></strong>
                                    </td>
                                    <td>
                                        <?php echo htmlspecialchars(substr($case['title'], 0, 30)) . (strlen($case['title']) > 30 ? '...' : ''); ?>
                                    </td>
                                    <td>
                                        <span class="badge badge-<?php 
                                            echo $case['status'] === 'solved' ? 'success' : 
                                                ($case['status'] === 'ongoing' ? 'info' : 
                                                ($case['status'] === 'paused' ? 'warning' : 'secondary')); 
                                        ?>">
                                            <?php echo ucfirst($case['status']); ?>
                                        </span>
                                    </td>
                                    <td>
                                        <?php echo date('M j, Y', strtotime($case['created_at'])); ?>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            <?php endif; ?>
        </div>
    </div>
    
    <!-- Recent Complaints -->
    <div class="card animate-fade-in">
        <div class="card-header">
            <h3 class="card-title">
                <i class="fas fa-exclamation-circle"></i>
                Recent Complaints
            </h3>
            <a href="/admin/manage_complaints.php" class="btn btn-sm btn-outline">View All</a>
        </div>
        <div class="card-body" style="padding: 0;">
            <?php if (empty($recent_complaints)): ?>
                <div style="padding: 2rem; text-align: center; color: var(--text-muted);">
                    <i class="fas fa-inbox" style="font-size: 3rem; margin-bottom: 1rem; opacity: 0.3;"></i>
                    <p>No complaints found</p>
                </div>
            <?php else: ?>
                <div class="table-container">
                    <table class="table">
                        <thead>
                            <tr>
                                <th>Complaint #</th>
                                <th>Title</th>
                                <th>Status</th>
                                <th>Submitted</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($recent_complaints as $complaint): ?>
                                <tr>
                                    <td>
                                        <strong><?php echo htmlspecialchars($complaint['complaint_number']); ?></strong>
                                    </td>
                                    <td>
                                        <?php echo htmlspecialchars(substr($complaint['title'], 0, 30)) . (strlen($complaint['title']) > 30 ? '...' : ''); ?>
                                    </td>
                                    <td>
                                        <span class="badge badge-<?php 
                                            echo $complaint['status'] === 'resolved' ? 'success' : 
                                                ($complaint['status'] === 'investigating' ? 'info' : 
                                                ($complaint['status'] === 'under_review' ? 'warning' : 'secondary')); 
                                        ?>">
                                            <?php echo ucfirst(str_replace('_', ' ', $complaint['status'])); ?>
                                        </span>
                                    </td>
                                    <td>
                                        <?php echo date('M j, Y', strtotime($complaint['created_at'])); ?>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<!-- Quick Actions -->
<div class="quick-actions" style="margin-top: 3rem;">
    <h3 style="margin-bottom: 1.5rem;">
        <i class="fas fa-bolt"></i>
        Quick Actions
    </h3>
    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem;">
        <a href="/admin/manage_users.php" class="btn btn-primary">
            <i class="fas fa-users"></i>
            Manage Users
        </a>
        <a href="/admin/manage_cases.php" class="btn btn-success">
            <i class="fas fa-folder-plus"></i>
            Manage Cases
        </a>
        <a href="/admin/reports.php" class="btn btn-warning">
            <i class="fas fa-chart-bar"></i>
            Generate Reports
        </a>
        <a href="/admin/settings.php" class="btn btn-secondary">
            <i class="fas fa-cog"></i>
            System Settings
        </a>
    </div>
</div>

<script>
// Case Status Chart
const caseStatusCtx = document.getElementById('caseStatusChart').getContext('2d');
new Chart(caseStatusCtx, {
    type: 'doughnut',
    data: {
        labels: ['Ongoing', 'Solved', 'Paused', 'Closed'],
        datasets: [{
            data: [
                <?php echo $case_status['ongoing'] ?? 0; ?>,
                <?php echo $case_status['solved'] ?? 0; ?>,
                <?php echo $case_status['paused'] ?? 0; ?>,
                <?php echo $case_status['closed'] ?? 0; ?>
            ],
            backgroundColor: [
                'rgba(6, 182, 212, 0.8)',
                'rgba(16, 185, 129, 0.8)',
                'rgba(245, 158, 11, 0.8)',
                'rgba(100, 116, 139, 0.8)'
            ],
            borderColor: [
                'rgba(6, 182, 212, 1)',
                'rgba(16, 185, 129, 1)',
                'rgba(245, 158, 11, 1)',
                'rgba(100, 116, 139, 1)'
            ],
            borderWidth: 2
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                position: 'bottom',
                labels: {
                    color: 'rgba(248, 250, 252, 0.8)',
                    padding: 20
                }
            }
        }
    }
});

// User Distribution Chart
const userDistCtx = document.getElementById('userDistributionChart').getContext('2d');
new Chart(userDistCtx, {
    type: 'pie',
    data: {
        labels: ['Administrators', 'Police Officers', 'Citizens'],
        datasets: [{
            data: [
                <?php echo $user_stats['admin'] ?? 0; ?>,
                <?php echo $user_stats['police'] ?? 0; ?>,
                <?php echo $user_stats['citizen'] ?? 0; ?>
            ],
            backgroundColor: [
                'rgba(239, 68, 68, 0.8)',
                'rgba(37, 99, 235, 0.8)',
                'rgba(16, 185, 129, 0.8)'
            ],
            borderColor: [
                'rgba(239, 68, 68, 1)',
                'rgba(37, 99, 235, 1)',
                'rgba(16, 185, 129, 1)'
            ],
            borderWidth: 2
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                position: 'bottom',
                labels: {
                    color: 'rgba(248, 250, 252, 0.8)',
                    padding: 20
                }
            }
        }
    }
});
</script>

<style>
.stats-grid .card {
    transition: var(--transition);
}

.stats-grid .card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-xl);
}

.card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.quick-actions .btn {
    padding: 1rem;
    text-align: center;
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    align-items: center;
}

.quick-actions .btn i {
    font-size: 1.5rem;
}

@media (max-width: 768px) {
    .charts-section,
    .recent-activity {
        grid-template-columns: 1fr !important;
    }
    
    .stats-grid {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)) !important;
    }
}
</style>

<?php require_once '../includes/footer.php'; ?>
