<?php
$page_title = 'Edit User';
require_once '../config/database.php';
requireAuth('admin');

$user_id = (int)($_GET['id'] ?? 0);
$error = '';
$success = '';

if (!$user_id) {
    header('Location: /admin/manage_users.php');
    exit();
}

$database = new Database();
$conn = $database->getConnection();

// Get user data
try {
    $stmt = $conn->prepare("SELECT * FROM users WHERE id = ?");
    $stmt->execute([$user_id]);
    $user = $stmt->fetch();
    
    if (!$user) {
        $_SESSION['flash_message'] = 'User not found.';
        $_SESSION['flash_type'] = 'error';
        header('Location: /admin/manage_users.php');
        exit();
    }
} catch (PDOException $e) {
    $error = 'Database error. Please try again later.';
}

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $username = sanitizeInput($_POST['username']);
    $email = sanitizeInput($_POST['email']);
    $full_name = sanitizeInput($_POST['full_name']);
    $role = $_POST['role'];
    $phone = sanitizeInput($_POST['phone']);
    $address = sanitizeInput($_POST['address']);
    $badge_number = sanitizeInput($_POST['badge_number']);
    $status = $_POST['status'];
    
    if (empty($username) || empty($email) || empty($full_name) || empty($role)) {
        $error = 'Please fill in all required fields.';
    } elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $error = 'Please enter a valid email address.';
    } else {
        try {
            // Check if username or email already exists (excluding current user)
            $stmt = $conn->prepare("SELECT id FROM users WHERE (username = ? OR email = ?) AND id != ?");
            $stmt->execute([$username, $email, $user_id]);
            
            if ($stmt->fetch()) {
                $error = 'Username or email already exists.';
            } else {
                $stmt = $conn->prepare("
                    UPDATE users 
                    SET username = ?, email = ?, full_name = ?, role = ?, phone = ?, address = ?, badge_number = ?, status = ?
                    WHERE id = ?
                ");
                
                if ($stmt->execute([$username, $email, $full_name, $role, $phone, $address, $badge_number, $status, $user_id])) {
                    $_SESSION['flash_message'] = 'User updated successfully.';
                    $_SESSION['flash_type'] = 'success';
                    header('Location: /admin/manage_users.php');
                    exit();
                } else {
                    $error = 'Failed to update user. Please try again.';
                }
            }
        } catch (PDOException $e) {
            $error = 'Database error. Please try again later.';
        }
    }
}

require_once '../includes/header.php';
?>

<div class="page-header" style="margin-bottom: 2rem;">
    <h1><i class="fas fa-user-edit"></i> Edit User</h1>
    <a href="/admin/manage_users.php" class="btn btn-outline">
        <i class="fas fa-arrow-left"></i> Back to Users
    </a>
</div>

<div class="card" style="max-width: 600px; margin: 0 auto;">
    <div class="card-body">
        <?php if ($error): ?>
            <div class="alert alert-error" style="margin-bottom: 1.5rem;">
                <i class="fas fa-exclamation-circle"></i>
                <?php echo htmlspecialchars($error); ?>
            </div>
        <?php endif; ?>
        
        <form method="POST" data-validate>
            <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
            
            <div class="form-group">
                <label class="form-label">Full Name *</label>
                <input type="text" name="full_name" class="form-control" required 
                       value="<?php echo htmlspecialchars($user['full_name']); ?>">
            </div>
            
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 1rem;">
                <div class="form-group">
                    <label class="form-label">Username *</label>
                    <input type="text" name="username" class="form-control" required 
                           value="<?php echo htmlspecialchars($user['username']); ?>">
                </div>
                <div class="form-group">
                    <label class="form-label">Email *</label>
                    <input type="email" name="email" class="form-control" required 
                           value="<?php echo htmlspecialchars($user['email']); ?>">
                </div>
            </div>
            
            <div style="display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 1rem;">
                <div class="form-group">
                    <label class="form-label">Role *</label>
                    <select name="role" class="form-control" required onchange="toggleBadgeField(this.value)">
                        <option value="admin" <?php echo $user['role'] === 'admin' ? 'selected' : ''; ?>>Administrator</option>
                        <option value="police" <?php echo $user['role'] === 'police' ? 'selected' : ''; ?>>Police Officer</option>
                        <option value="citizen" <?php echo $user['role'] === 'citizen' ? 'selected' : ''; ?>>Citizen</option>
                    </select>
                </div>
                <div class="form-group">
                    <label class="form-label">Status *</label>
                    <select name="status" class="form-control" required>
                        <option value="active" <?php echo $user['status'] === 'active' ? 'selected' : ''; ?>>Active</option>
                        <option value="inactive" <?php echo $user['status'] === 'inactive' ? 'selected' : ''; ?>>Inactive</option>
                    </select>
                </div>
                <div class="form-group" id="badge-field" style="<?php echo $user['role'] === 'police' ? '' : 'display: none;'; ?>">
                    <label class="form-label">Badge Number</label>
                    <input type="text" name="badge_number" class="form-control" 
                           value="<?php echo htmlspecialchars($user['badge_number'] ?? ''); ?>">
                </div>
            </div>
            
            <div class="form-group">
                <label class="form-label">Phone</label>
                <input type="tel" name="phone" class="form-control" 
                       value="<?php echo htmlspecialchars($user['phone'] ?? ''); ?>">
            </div>
            
            <div class="form-group">
                <label class="form-label">Address</label>
                <textarea name="address" class="form-control" rows="3"><?php echo htmlspecialchars($user['address'] ?? ''); ?></textarea>
            </div>
            
            <div style="display: flex; gap: 1rem; justify-content: end; margin-top: 2rem;">
                <a href="/admin/manage_users.php" class="btn btn-outline">Cancel</a>
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-save"></i> Update User
                </button>
            </div>
        </form>
    </div>
</div>

<style>
.alert {
    padding: 1rem;
    border-radius: var(--border-radius);
    border: 1px solid;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.alert-error {
    background: rgba(239, 68, 68, 0.1);
    border-color: var(--danger-color);
    color: var(--danger-color);
}

.page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
}
</style>

<script>
function toggleBadgeField(role) {
    const badgeField = document.getElementById('badge-field');
    if (role === 'police') {
        badgeField.style.display = 'block';
    } else {
        badgeField.style.display = 'none';
    }
}
</script>

<?php require_once '../includes/footer.php'; ?>
