<?php
$page_title = 'Reports';
require_once '../config/database.php';
requireAuth('admin');

$database = new Database();
$conn = $database->getConnection();

// Handle report generation
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['generate_report'])) {
    $report_type = $_POST['report_type'];
    $date_from = $_POST['date_from'];
    $date_to = $_POST['date_to'];
    
    header('Content-Type: text/csv');
    header('Content-Disposition: attachment; filename="' . $report_type . '_report_' . date('Y-m-d') . '.csv"');
    
    $output = fopen('php://output', 'w');
    
    switch ($report_type) {
        case 'cases':
            fputcsv($output, ['Case Number', 'Title', 'Crime Type', 'Status', 'Priority', 'Created Date', 'Officer']);
            $stmt = $conn->prepare("
                SELECT cr.case_number, cr.title, cr.crime_type, cr.status, cr.priority, cr.created_at, u.full_name 
                FROM criminal_records cr 
                LEFT JOIN users u ON cr.assigned_officer_id = u.id 
                WHERE cr.created_at BETWEEN ? AND ?
            ");
            $stmt->execute([$date_from, $date_to]);
            while ($row = $stmt->fetch()) {
                fputcsv($output, $row);
            }
            break;
            
        case 'users':
            fputcsv($output, ['Full Name', 'Username', 'Email', 'Role', 'Status', 'Created Date']);
            $stmt = $conn->prepare("SELECT full_name, username, email, role, status, created_at FROM users WHERE created_at BETWEEN ? AND ?");
            $stmt->execute([$date_from, $date_to]);
            while ($row = $stmt->fetch()) {
                fputcsv($output, $row);
            }
            break;
            
        case 'complaints':
            fputcsv($output, ['Complaint Number', 'Title', 'Category', 'Status', 'Priority', 'Created Date', 'Citizen']);
            $stmt = $conn->prepare("
                SELECT c.complaint_number, c.title, c.category, c.status, c.priority, c.created_at, u.full_name 
                FROM complaints c 
                LEFT JOIN users u ON c.citizen_id = u.id 
                WHERE c.created_at BETWEEN ? AND ?
            ");
            $stmt->execute([$date_from, $date_to]);
            while ($row = $stmt->fetch()) {
                fputcsv($output, $row);
            }
            break;
    }
    
    fclose($output);
    exit();
}

// Get statistics for reports
$stats = [];

// Cases statistics
$stmt = $conn->query("SELECT status, COUNT(*) as count FROM criminal_records GROUP BY status");
$case_stats = [];
while ($row = $stmt->fetch()) {
    $case_stats[$row['status']] = $row['count'];
}

// Monthly case trends
$stmt = $conn->query("
    SELECT DATE_FORMAT(created_at, '%Y-%m') as month, COUNT(*) as count 
    FROM criminal_records 
    WHERE created_at >= DATE_SUB(NOW(), INTERVAL 12 MONTH) 
    GROUP BY month 
    ORDER BY month
");
$monthly_cases = $stmt->fetchAll();

// User statistics
$stmt = $conn->query("SELECT role, COUNT(*) as count FROM users GROUP BY role");
$user_stats = [];
while ($row = $stmt->fetch()) {
    $user_stats[$row['role']] = $row['count'];
}

require_once '../includes/header.php';
?>

<div class="page-header" style="margin-bottom: 2rem;">
    <h1><i class="fas fa-chart-bar"></i> Reports</h1>
</div>

<!-- Report Generation -->
<div class="card" style="margin-bottom: 2rem;">
    <div class="card-header">
        <h3 class="card-title"><i class="fas fa-download"></i> Generate Reports</h3>
    </div>
    <div class="card-body">
        <form method="POST">
            <div style="display: grid; grid-template-columns: 1fr 1fr 1fr auto; gap: 1rem; align-items: end;">
                <div class="form-group" style="margin: 0;">
                    <label class="form-label">Report Type</label>
                    <select name="report_type" class="form-control" required>
                        <option value="">Select Report</option>
                        <option value="cases">Cases Report</option>
                        <option value="users">Users Report</option>
                        <option value="complaints">Complaints Report</option>
                    </select>
                </div>
                <div class="form-group" style="margin: 0;">
                    <label class="form-label">From Date</label>
                    <input type="date" name="date_from" class="form-control" value="<?php echo date('Y-m-01'); ?>" required>
                </div>
                <div class="form-group" style="margin: 0;">
                    <label class="form-label">To Date</label>
                    <input type="date" name="date_to" class="form-control" value="<?php echo date('Y-m-d'); ?>" required>
                </div>
                <button type="submit" name="generate_report" class="btn btn-primary">
                    <i class="fas fa-download"></i> Download CSV
                </button>
            </div>
        </form>
    </div>
</div>

<!-- Statistics Overview -->
<div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 2rem; margin-bottom: 3rem;">
    
    <!-- Case Statistics -->
    <div class="card">
        <div class="card-header">
            <h3 class="card-title"><i class="fas fa-folder-open"></i> Case Statistics</h3>
        </div>
        <div class="card-body">
            <canvas id="caseStatsChart" height="200"></canvas>
        </div>
    </div>
    
    <!-- User Distribution -->
    <div class="card">
        <div class="card-header">
            <h3 class="card-title"><i class="fas fa-users"></i> User Distribution</h3>
        </div>
        <div class="card-body">
            <canvas id="userStatsChart" height="200"></canvas>
        </div>
    </div>
</div>

<!-- Monthly Trends -->
<div class="card">
    <div class="card-header">
        <h3 class="card-title"><i class="fas fa-chart-line"></i> Monthly Case Trends</h3>
    </div>
    <div class="card-body">
        <canvas id="monthlyTrendsChart" height="300"></canvas>
    </div>
</div>

<!-- Summary Tables -->
<div style="display: grid; grid-template-columns: 1fr 1fr; gap: 2rem; margin-top: 2rem;">
    
    <!-- Case Summary -->
    <div class="card">
        <div class="card-header">
            <h3 class="card-title">Case Summary</h3>
        </div>
        <div class="card-body" style="padding: 0;">
            <table class="table">
                <thead>
                    <tr>
                        <th>Status</th>
                        <th>Count</th>
                        <th>Percentage</th>
                    </tr>
                </thead>
                <tbody>
                    <?php 
                    $total_cases = array_sum($case_stats);
                    foreach ($case_stats as $status => $count): 
                        $percentage = $total_cases > 0 ? round(($count / $total_cases) * 100, 1) : 0;
                    ?>
                        <tr>
                            <td>
                                <span class="badge badge-<?php 
                                    echo $status === 'solved' ? 'success' : 
                                        ($status === 'ongoing' ? 'info' : 
                                        ($status === 'paused' ? 'warning' : 'secondary')); 
                                ?>">
                                    <?php echo ucfirst($status); ?>
                                </span>
                            </td>
                            <td><?php echo number_format($count); ?></td>
                            <td><?php echo $percentage; ?>%</td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
    </div>
    
    <!-- User Summary -->
    <div class="card">
        <div class="card-header">
            <h3 class="card-title">User Summary</h3>
        </div>
        <div class="card-body" style="padding: 0;">
            <table class="table">
                <thead>
                    <tr>
                        <th>Role</th>
                        <th>Count</th>
                        <th>Percentage</th>
                    </tr>
                </thead>
                <tbody>
                    <?php 
                    $total_users = array_sum($user_stats);
                    foreach ($user_stats as $role => $count): 
                        $percentage = $total_users > 0 ? round(($count / $total_users) * 100, 1) : 0;
                    ?>
                        <tr>
                            <td>
                                <span class="badge badge-<?php 
                                    echo $role === 'admin' ? 'danger' : 
                                        ($role === 'police' ? 'primary' : 'success'); 
                                ?>">
                                    <?php echo ucfirst($role); ?>
                                </span>
                            </td>
                            <td><?php echo number_format($count); ?></td>
                            <td><?php echo $percentage; ?>%</td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
    </div>
</div>

<script>
// Case Statistics Chart
const caseStatsCtx = document.getElementById('caseStatsChart').getContext('2d');
new Chart(caseStatsCtx, {
    type: 'doughnut',
    data: {
        labels: <?php echo json_encode(array_keys($case_stats)); ?>,
        datasets: [{
            data: <?php echo json_encode(array_values($case_stats)); ?>,
            backgroundColor: [
                'rgba(6, 182, 212, 0.8)',
                'rgba(16, 185, 129, 0.8)',
                'rgba(245, 158, 11, 0.8)',
                'rgba(100, 116, 139, 0.8)'
            ]
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                position: 'bottom',
                labels: { color: 'rgba(248, 250, 252, 0.8)' }
            }
        }
    }
});

// User Statistics Chart
const userStatsCtx = document.getElementById('userStatsChart').getContext('2d');
new Chart(userStatsCtx, {
    type: 'pie',
    data: {
        labels: <?php echo json_encode(array_keys($user_stats)); ?>,
        datasets: [{
            data: <?php echo json_encode(array_values($user_stats)); ?>,
            backgroundColor: [
                'rgba(239, 68, 68, 0.8)',
                'rgba(37, 99, 235, 0.8)',
                'rgba(16, 185, 129, 0.8)'
            ]
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                position: 'bottom',
                labels: { color: 'rgba(248, 250, 252, 0.8)' }
            }
        }
    }
});

// Monthly Trends Chart
const monthlyTrendsCtx = document.getElementById('monthlyTrendsChart').getContext('2d');
new Chart(monthlyTrendsCtx, {
    type: 'line',
    data: {
        labels: <?php echo json_encode(array_column($monthly_cases, 'month')); ?>,
        datasets: [{
            label: 'Cases',
            data: <?php echo json_encode(array_column($monthly_cases, 'count')); ?>,
            borderColor: 'rgba(37, 99, 235, 1)',
            backgroundColor: 'rgba(37, 99, 235, 0.1)',
            tension: 0.4,
            fill: true
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                labels: { color: 'rgba(248, 250, 252, 0.8)' }
            }
        },
        scales: {
            y: {
                beginAtZero: true,
                ticks: { color: 'rgba(248, 250, 252, 0.8)' },
                grid: { color: 'rgba(100, 116, 139, 0.3)' }
            },
            x: {
                ticks: { color: 'rgba(248, 250, 252, 0.8)' },
                grid: { color: 'rgba(100, 116, 139, 0.3)' }
            }
        }
    }
});
</script>

<?php require_once '../includes/footer.php'; ?>
