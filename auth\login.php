<?php
$page_title = 'Login';
require_once '../config/database.php';

// Redirect if already logged in
if (isLoggedIn()) {
    $role = $_SESSION['role'];
    switch ($role) {
        case 'admin':
            header('Location: /admin/dashboard.php');
            break;
        case 'police':
            header('Location: /police/dashboard.php');
            break;
        case 'citizen':
            header('Location: /citizen/dashboard.php');
            break;
    }
    exit();
}

$error = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $username = sanitizeInput($_POST['username']);
    $password = $_POST['password'];
    
    if (empty($username) || empty($password)) {
        $error = 'Please fill in all fields.';
    } else {
        try {
            $database = new Database();
            $conn = $database->getConnection();
            
            $stmt = $conn->prepare("SELECT id, username, password, full_name, role, status FROM users WHERE username = ? OR email = ?");
            $stmt->execute([$username, $username]);
            $user = $stmt->fetch();
            
            if ($user && password_verify($password, $user['password'])) {
                if ($user['status'] === 'inactive') {
                    $error = 'Your account has been deactivated. Please contact the administrator.';
                } else {
                    // Login successful
                    startSession();
                    $_SESSION['user_id'] = $user['id'];
                    $_SESSION['username'] = $user['username'];
                    $_SESSION['full_name'] = $user['full_name'];
                    $_SESSION['role'] = $user['role'];
                    
                    // Update last login
                    $updateStmt = $conn->prepare("UPDATE users SET updated_at = CURRENT_TIMESTAMP WHERE id = ?");
                    $updateStmt->execute([$user['id']]);
                    
                    // Redirect based on role
                    switch ($user['role']) {
                        case 'admin':
                            header('Location: /admin/dashboard.php');
                            break;
                        case 'police':
                            header('Location: /police/dashboard.php');
                            break;
                        case 'citizen':
                            header('Location: /citizen/dashboard.php');
                            break;
                    }
                    exit();
                }
            } else {
                $error = 'Invalid username or password.';
            }
        } catch (PDOException $e) {
            $error = 'Database error. Please try again later.';
        }
    }
}

require_once '../includes/header.php';
?>

<div class="auth-container" style="max-width: 400px; margin: 2rem auto; padding: 0 1rem;">
    <div class="card animate-fade-in">
        <div class="card-header" style="text-align: center;">
            <img src="/assets/police-force(3).png" alt="LPF Logo" style="width: 80px; height: 80px; margin-bottom: 1rem; border-radius: 50%;">
            <h2 class="card-title">Login to CRMS</h2>
            <p style="color: var(--text-muted); margin: 0;">Lesotho Police Force</p>
        </div>
        
        <div class="card-body">
            <?php if ($error): ?>
                <div class="alert alert-error" style="margin-bottom: 1.5rem;">
                    <i class="fas fa-exclamation-circle"></i>
                    <?php echo htmlspecialchars($error); ?>
                </div>
            <?php endif; ?>
            
            <form method="POST" data-validate>
                <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                
                <div class="form-group">
                    <label for="username" class="form-label">
                        <i class="fas fa-user"></i>
                        Username or Email
                    </label>
                    <input 
                        type="text" 
                        id="username" 
                        name="username" 
                        class="form-control" 
                        placeholder="Enter your username or email"
                        value="<?php echo isset($_POST['username']) ? htmlspecialchars($_POST['username']) : ''; ?>"
                        required
                        autocomplete="username"
                    >
                </div>
                
                <div class="form-group">
                    <label for="password" class="form-label">
                        <i class="fas fa-lock"></i>
                        Password
                    </label>
                    <div style="position: relative;">
                        <input 
                            type="password" 
                            id="password" 
                            name="password" 
                            class="form-control" 
                            placeholder="Enter your password"
                            required
                            autocomplete="current-password"
                        >
                        <button 
                            type="button" 
                            onclick="togglePassword()" 
                            style="position: absolute; right: 0.75rem; top: 50%; transform: translateY(-50%); background: none; border: none; color: var(--text-muted); cursor: pointer;"
                        >
                            <i id="password-toggle-icon" class="fas fa-eye"></i>
                        </button>
                    </div>
                </div>
                
                <div class="form-group" style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 2rem;">
                    <label style="display: flex; align-items: center; gap: 0.5rem; cursor: pointer;">
                        <input type="checkbox" name="remember" style="margin: 0;">
                        <span style="font-size: 0.875rem; color: var(--text-muted);">Remember me</span>
                    </label>
                    <a href="/auth/forgot-password.php" style="font-size: 0.875rem; color: var(--primary-color); text-decoration: none;">
                        Forgot password?
                    </a>
                </div>
                
                <button type="submit" class="btn btn-primary w-full">
                    <i class="fas fa-sign-in-alt"></i>
                    Login
                </button>
            </form>
            
            <div style="text-align: center; margin-top: 2rem; padding-top: 1.5rem; border-top: 1px solid var(--border-color);">
                <p style="color: var(--text-muted); margin-bottom: 1rem;">Don't have an account?</p>
                <a href="/auth/register.php" class="btn btn-outline w-full">
                    <i class="fas fa-user-plus"></i>
                    Register New Account
                </a>
            </div>
        </div>
    </div>
    
    <!-- Demo Credentials -->
    <div class="card" style="margin-top: 2rem;">
        <div class="card-header">
            <h3 class="card-title" style="font-size: 1rem;">
                <i class="fas fa-info-circle"></i>
                Demo Credentials
            </h3>
        </div>
        <div class="card-body" style="padding: 1rem;">
            <div style="display: grid; gap: 1rem; font-size: 0.875rem;">
                <div>
                    <strong style="color: var(--danger-color);">Administrator:</strong><br>
                    Username: <code>admin</code><br>
                    Password: <code>password</code>
                </div>
                <div>
                    <strong style="color: var(--primary-color);">Police Officer:</strong><br>
                    Username: <code>officer1</code><br>
                    Password: <code>password</code>
                </div>
                <div>
                    <strong style="color: var(--success-color);">Citizen:</strong><br>
                    Username: <code>citizen1</code><br>
                    Password: <code>password</code>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.alert {
    padding: 1rem;
    border-radius: var(--border-radius);
    border: 1px solid;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.alert-error {
    background: rgba(239, 68, 68, 0.1);
    border-color: var(--danger-color);
    color: var(--danger-color);
}

.auth-container .card {
    box-shadow: var(--shadow-xl);
}

.auth-container .form-control:focus {
    transform: translateY(-1px);
}

.auth-container .btn:hover {
    transform: translateY(-2px);
}

code {
    background: var(--bg-tertiary);
    padding: 0.2rem 0.4rem;
    border-radius: 4px;
    font-family: 'Courier New', monospace;
    font-size: 0.8rem;
}

@media (max-width: 480px) {
    .auth-container {
        margin: 1rem auto;
        padding: 0 0.5rem;
    }
    
    .card-body {
        padding: 1rem !important;
    }
}
</style>

<script>
function togglePassword() {
    const passwordInput = document.getElementById('password');
    const toggleIcon = document.getElementById('password-toggle-icon');
    
    if (passwordInput.type === 'password') {
        passwordInput.type = 'text';
        toggleIcon.className = 'fas fa-eye-slash';
    } else {
        passwordInput.type = 'password';
        toggleIcon.className = 'fas fa-eye';
    }
}

// Auto-focus on username field
document.addEventListener('DOMContentLoaded', function() {
    document.getElementById('username').focus();
});
</script>

<?php require_once '../includes/footer.php'; ?>
