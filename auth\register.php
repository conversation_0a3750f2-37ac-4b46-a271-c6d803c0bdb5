<?php
$page_title = 'Register';
require_once '../config/database.php';

// Redirect if already logged in
if (isLoggedIn()) {
    header('Location: /index.php');
    exit();
}

$error = '';
$success = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $username = sanitizeInput($_POST['username']);
    $email = sanitizeInput($_POST['email']);
    $password = $_POST['password'];
    $confirm_password = $_POST['confirm_password'];
    $full_name = sanitizeInput($_POST['full_name']);
    $phone = sanitizeInput($_POST['phone']);
    $address = sanitizeInput($_POST['address']);
    $role = 'citizen'; // Default role for registration
    
    // Validation
    if (empty($username) || empty($email) || empty($password) || empty($full_name)) {
        $error = 'Please fill in all required fields.';
    } elseif ($password !== $confirm_password) {
        $error = 'Passwords do not match.';
    } elseif (strlen($password) < 6) {
        $error = 'Password must be at least 6 characters long.';
    } elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $error = 'Please enter a valid email address.';
    } else {
        try {
            $database = new Database();
            $conn = $database->getConnection();
            
            // Check if username or email already exists
            $stmt = $conn->prepare("SELECT id FROM users WHERE username = ? OR email = ?");
            $stmt->execute([$username, $email]);
            
            if ($stmt->fetch()) {
                $error = 'Username or email already exists.';
            } else {
                // Hash password
                $hashed_password = password_hash($password, PASSWORD_DEFAULT);
                
                // Insert new user
                $stmt = $conn->prepare("
                    INSERT INTO users (username, email, password, full_name, role, phone, address) 
                    VALUES (?, ?, ?, ?, ?, ?, ?)
                ");
                
                if ($stmt->execute([$username, $email, $hashed_password, $full_name, $role, $phone, $address])) {
                    $success = 'Registration successful! You can now login with your credentials.';
                    // Clear form data
                    $_POST = [];
                } else {
                    $error = 'Registration failed. Please try again.';
                }
            }
        } catch (PDOException $e) {
            $error = 'Database error. Please try again later.';
        }
    }
}

require_once '../includes/header.php';
?>

<div class="auth-container" style="max-width: 500px; margin: 2rem auto; padding: 0 1rem;">
    <div class="card animate-fade-in">
        <div class="card-header" style="text-align: center;">
            <img src="/assets/police-force(3).png" alt="LPF Logo" style="width: 80px; height: 80px; margin-bottom: 1rem; border-radius: 50%;">
            <h2 class="card-title">Register for CRMS</h2>
            <p style="color: var(--text-muted); margin: 0;">Create your citizen account</p>
        </div>
        
        <div class="card-body">
            <?php if ($error): ?>
                <div class="alert alert-error" style="margin-bottom: 1.5rem;">
                    <i class="fas fa-exclamation-circle"></i>
                    <?php echo htmlspecialchars($error); ?>
                </div>
            <?php endif; ?>
            
            <?php if ($success): ?>
                <div class="alert alert-success" style="margin-bottom: 1.5rem;">
                    <i class="fas fa-check-circle"></i>
                    <?php echo htmlspecialchars($success); ?>
                </div>
            <?php endif; ?>
            
            <form method="POST" data-validate>
                <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                
                <div class="form-group">
                    <label for="full_name" class="form-label">
                        <i class="fas fa-user"></i>
                        Full Name *
                    </label>
                    <input 
                        type="text" 
                        id="full_name" 
                        name="full_name" 
                        class="form-control" 
                        placeholder="Enter your full name"
                        value="<?php echo isset($_POST['full_name']) ? htmlspecialchars($_POST['full_name']) : ''; ?>"
                        required
                    >
                </div>
                
                <div class="form-group">
                    <label for="username" class="form-label">
                        <i class="fas fa-at"></i>
                        Username *
                    </label>
                    <input 
                        type="text" 
                        id="username" 
                        name="username" 
                        class="form-control" 
                        placeholder="Choose a username"
                        value="<?php echo isset($_POST['username']) ? htmlspecialchars($_POST['username']) : ''; ?>"
                        required
                        pattern="[a-zA-Z0-9_]{3,20}"
                        title="Username must be 3-20 characters long and contain only letters, numbers, and underscores"
                    >
                </div>
                
                <div class="form-group">
                    <label for="email" class="form-label">
                        <i class="fas fa-envelope"></i>
                        Email Address *
                    </label>
                    <input 
                        type="email" 
                        id="email" 
                        name="email" 
                        class="form-control" 
                        placeholder="Enter your email address"
                        value="<?php echo isset($_POST['email']) ? htmlspecialchars($_POST['email']) : ''; ?>"
                        required
                    >
                </div>
                
                <div class="form-group">
                    <label for="phone" class="form-label">
                        <i class="fas fa-phone"></i>
                        Phone Number
                    </label>
                    <input 
                        type="tel" 
                        id="phone" 
                        name="phone" 
                        class="form-control" 
                        placeholder="e.g., +266-1234-5678"
                        value="<?php echo isset($_POST['phone']) ? htmlspecialchars($_POST['phone']) : ''; ?>"
                    >
                </div>
                
                <div class="form-group">
                    <label for="address" class="form-label">
                        <i class="fas fa-map-marker-alt"></i>
                        Address
                    </label>
                    <textarea 
                        id="address" 
                        name="address" 
                        class="form-control" 
                        placeholder="Enter your address"
                        rows="3"
                    ><?php echo isset($_POST['address']) ? htmlspecialchars($_POST['address']) : ''; ?></textarea>
                </div>
                
                <div class="form-group">
                    <label for="password" class="form-label">
                        <i class="fas fa-lock"></i>
                        Password *
                    </label>
                    <div style="position: relative;">
                        <input 
                            type="password" 
                            id="password" 
                            name="password" 
                            class="form-control" 
                            placeholder="Create a password (min. 6 characters)"
                            required
                            minlength="6"
                        >
                        <button 
                            type="button" 
                            onclick="togglePassword('password')" 
                            style="position: absolute; right: 0.75rem; top: 50%; transform: translateY(-50%); background: none; border: none; color: var(--text-muted); cursor: pointer;"
                        >
                            <i id="password-toggle-icon" class="fas fa-eye"></i>
                        </button>
                    </div>
                </div>
                
                <div class="form-group">
                    <label for="confirm_password" class="form-label">
                        <i class="fas fa-lock"></i>
                        Confirm Password *
                    </label>
                    <div style="position: relative;">
                        <input 
                            type="password" 
                            id="confirm_password" 
                            name="confirm_password" 
                            class="form-control" 
                            placeholder="Confirm your password"
                            required
                            minlength="6"
                        >
                        <button 
                            type="button" 
                            onclick="togglePassword('confirm_password')" 
                            style="position: absolute; right: 0.75rem; top: 50%; transform: translateY(-50%); background: none; border: none; color: var(--text-muted); cursor: pointer;"
                        >
                            <i id="confirm-password-toggle-icon" class="fas fa-eye"></i>
                        </button>
                    </div>
                </div>
                
                <div class="form-group" style="margin-bottom: 2rem;">
                    <label style="display: flex; align-items: start; gap: 0.5rem; cursor: pointer;">
                        <input type="checkbox" required style="margin-top: 0.2rem;">
                        <span style="font-size: 0.875rem; color: var(--text-muted); line-height: 1.4;">
                            I agree to the <a href="/terms.php" style="color: var(--primary-color);">Terms of Service</a> 
                            and <a href="/privacy.php" style="color: var(--primary-color);">Privacy Policy</a>
                        </span>
                    </label>
                </div>
                
                <button type="submit" class="btn btn-primary w-full">
                    <i class="fas fa-user-plus"></i>
                    Create Account
                </button>
            </form>
            
            <div style="text-align: center; margin-top: 2rem; padding-top: 1.5rem; border-top: 1px solid var(--border-color);">
                <p style="color: var(--text-muted); margin-bottom: 1rem;">Already have an account?</p>
                <a href="/auth/login.php" class="btn btn-outline w-full">
                    <i class="fas fa-sign-in-alt"></i>
                    Login to Your Account
                </a>
            </div>
        </div>
    </div>
</div>

<style>
.alert {
    padding: 1rem;
    border-radius: var(--border-radius);
    border: 1px solid;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.alert-error {
    background: rgba(239, 68, 68, 0.1);
    border-color: var(--danger-color);
    color: var(--danger-color);
}

.alert-success {
    background: rgba(16, 185, 129, 0.1);
    border-color: var(--success-color);
    color: var(--success-color);
}

.auth-container .card {
    box-shadow: var(--shadow-xl);
}

.form-group {
    position: relative;
}

.form-control:focus {
    transform: translateY(-1px);
}

.btn:hover {
    transform: translateY(-2px);
}

@media (max-width: 480px) {
    .auth-container {
        margin: 1rem auto;
        padding: 0 0.5rem;
    }
    
    .card-body {
        padding: 1rem !important;
    }
}
</style>

<script>
function togglePassword(fieldId) {
    const passwordInput = document.getElementById(fieldId);
    const toggleIcon = document.getElementById(fieldId + '-toggle-icon');
    
    if (passwordInput.type === 'password') {
        passwordInput.type = 'text';
        toggleIcon.className = 'fas fa-eye-slash';
    } else {
        passwordInput.type = 'password';
        toggleIcon.className = 'fas fa-eye';
    }
}

// Password confirmation validation
document.addEventListener('DOMContentLoaded', function() {
    const password = document.getElementById('password');
    const confirmPassword = document.getElementById('confirm_password');
    
    function validatePassword() {
        if (password.value !== confirmPassword.value) {
            confirmPassword.setCustomValidity('Passwords do not match');
        } else {
            confirmPassword.setCustomValidity('');
        }
    }
    
    password.addEventListener('input', validatePassword);
    confirmPassword.addEventListener('input', validatePassword);
    
    // Auto-focus on full name field
    document.getElementById('full_name').focus();
});
</script>

<?php require_once '../includes/footer.php'; ?>
