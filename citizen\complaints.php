<?php
$page_title = 'Complaints';
require_once '../config/database.php';
requireAuth('citizen');

$database = new Database();
$conn = $database->getConnection();
$citizen_id = $_SESSION['user_id'];

// Handle new complaint submission
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['submit_complaint'])) {
    $title = sanitizeInput($_POST['title']);
    $description = sanitizeInput($_POST['description']);
    $category = sanitizeInput($_POST['category']);
    $location = sanitizeInput($_POST['location']);
    $incident_date = $_POST['incident_date'];
    $priority = $_POST['priority'];
    
    if (empty($title) || empty($description) || empty($category)) {
        $_SESSION['flash_message'] = 'Please fill in all required fields.';
        $_SESSION['flash_type'] = 'error';
    } else {
        try {
            // Generate complaint number
            $complaint_number = 'CMP-' . date('Y') . '-' . str_pad(rand(1, 9999), 4, '0', STR_PAD_LEFT);
            
            $stmt = $conn->prepare("
                INSERT INTO complaints (complaint_number, citizen_id, title, description, category, location, incident_date, priority) 
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            ");
            
            if ($stmt->execute([$complaint_number, $citizen_id, $title, $description, $category, $location, $incident_date, $priority])) {
                $_SESSION['flash_message'] = 'Complaint submitted successfully. Reference: ' . $complaint_number;
                $_SESSION['flash_type'] = 'success';
            } else {
                $_SESSION['flash_message'] = 'Failed to submit complaint. Please try again.';
                $_SESSION['flash_type'] = 'error';
            }
        } catch (PDOException $e) {
            $_SESSION['flash_message'] = 'Database error. Please try again later.';
            $_SESSION['flash_type'] = 'error';
        }
        header('Location: /citizen/complaints.php');
        exit();
    }
}

// Get complaints with search and filter
$search = $_GET['search'] ?? '';
$status_filter = $_GET['status'] ?? '';

$where_conditions = ["c.citizen_id = ?"];
$params = [$citizen_id];

if ($search) {
    $where_conditions[] = "(complaint_number LIKE ? OR title LIKE ? OR category LIKE ?)";
    $params[] = "%$search%";
    $params[] = "%$search%";
    $params[] = "%$search%";
}

if ($status_filter) {
    $where_conditions[] = "c.status = ?";
    $params[] = $status_filter;
}

$where_clause = 'WHERE ' . implode(' AND ', $where_conditions);

$stmt = $conn->prepare("
    SELECT c.*, u.full_name as officer_name 
    FROM complaints c 
    LEFT JOIN users u ON c.assigned_officer_id = u.id 
    $where_clause 
    ORDER BY c.created_at DESC
");
$stmt->execute($params);
$complaints = $stmt->fetchAll();

require_once '../includes/header.php';
?>

<div class="page-header" style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 2rem;">
    <h1><i class="fas fa-exclamation-triangle"></i> My Complaints</h1>
    <button onclick="openModal('complaintModal')" class="btn btn-primary">
        <i class="fas fa-plus"></i> File Complaint
    </button>
</div>

<!-- Filters -->
<div class="card" style="margin-bottom: 2rem;">
    <div class="card-body">
        <form method="GET" style="display: grid; grid-template-columns: 1fr 200px auto; gap: 1rem; align-items: end;">
            <div class="form-group" style="margin: 0;">
                <label class="form-label">Search</label>
                <input type="text" name="search" class="form-control" placeholder="Complaint number, title, or category" value="<?php echo htmlspecialchars($search); ?>">
            </div>
            <div class="form-group" style="margin: 0;">
                <label class="form-label">Status</label>
                <select name="status" class="form-control">
                    <option value="">All Status</option>
                    <option value="submitted" <?php echo $status_filter === 'submitted' ? 'selected' : ''; ?>>Submitted</option>
                    <option value="under_review" <?php echo $status_filter === 'under_review' ? 'selected' : ''; ?>>Under Review</option>
                    <option value="investigating" <?php echo $status_filter === 'investigating' ? 'selected' : ''; ?>>Investigating</option>
                    <option value="resolved" <?php echo $status_filter === 'resolved' ? 'selected' : ''; ?>>Resolved</option>
                    <option value="closed" <?php echo $status_filter === 'closed' ? 'selected' : ''; ?>>Closed</option>
                </select>
            </div>
            <button type="submit" class="btn btn-secondary">Filter</button>
        </form>
    </div>
</div>

<!-- Complaints Table -->
<div class="card">
    <div class="card-body" style="padding: 0;">
        <div class="table-container">
            <table class="table">
                <thead>
                    <tr>
                        <th>Complaint</th>
                        <th>Category</th>
                        <th>Status</th>
                        <th>Priority</th>
                        <th>Officer</th>
                        <th>Submitted</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($complaints as $complaint): ?>
                        <tr>
                            <td>
                                <div>
                                    <strong><?php echo htmlspecialchars($complaint['complaint_number']); ?></strong><br>
                                    <small style="color: var(--text-muted);"><?php echo htmlspecialchars(substr($complaint['title'], 0, 40)) . (strlen($complaint['title']) > 40 ? '...' : ''); ?></small>
                                </div>
                            </td>
                            <td><?php echo htmlspecialchars($complaint['category']); ?></td>
                            <td>
                                <span class="badge badge-<?php 
                                    echo $complaint['status'] === 'resolved' ? 'success' : 
                                        ($complaint['status'] === 'investigating' ? 'info' : 
                                        ($complaint['status'] === 'under_review' ? 'warning' : 'secondary')); 
                                ?>">
                                    <?php echo ucfirst(str_replace('_', ' ', $complaint['status'])); ?>
                                </span>
                            </td>
                            <td>
                                <span class="badge badge-<?php 
                                    echo $complaint['priority'] === 'urgent' ? 'danger' : 
                                        ($complaint['priority'] === 'high' ? 'warning' : 
                                        ($complaint['priority'] === 'medium' ? 'info' : 'secondary')); 
                                ?>">
                                    <?php echo ucfirst($complaint['priority']); ?>
                                </span>
                            </td>
                            <td>
                                <?php echo $complaint['officer_name'] ? htmlspecialchars($complaint['officer_name']) : '<em>Not assigned</em>'; ?>
                            </td>
                            <td><?php echo date('M j, Y', strtotime($complaint['created_at'])); ?></td>
                            <td>
                                <a href="/citizen/view_complaint.php?id=<?php echo $complaint['id']; ?>" class="btn btn-sm btn-outline">
                                    <i class="fas fa-eye"></i>
                                </a>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
    </div>
</div>

<?php if (empty($complaints)): ?>
    <div style="text-align: center; padding: 3rem; color: var(--text-muted);">
        <i class="fas fa-exclamation-triangle" style="font-size: 4rem; margin-bottom: 1rem; opacity: 0.3;"></i>
        <h3>No complaints found</h3>
        <p>You haven't filed any complaints yet or try adjusting your search criteria.</p>
        <button onclick="openModal('complaintModal')" class="btn btn-primary" style="margin-top: 1rem;">
            <i class="fas fa-plus"></i> File Your First Complaint
        </button>
    </div>
<?php endif; ?>

<!-- Complaint Modal -->
<div id="complaintModal" class="modal" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 1000; align-items: center; justify-content: center;">
    <div class="modal-content" style="background: var(--bg-card); border-radius: var(--border-radius-lg); max-width: 600px; width: 90%; max-height: 90%; overflow-y: auto;">
        <div class="modal-header" style="padding: 1.5rem; border-bottom: 1px solid var(--border-color); display: flex; justify-content: space-between; align-items: center;">
            <h3><i class="fas fa-plus"></i> File New Complaint</h3>
            <button onclick="closeModal('complaintModal')" class="modal-close" style="background: none; border: none; font-size: 1.5rem; color: var(--text-muted); cursor: pointer;">&times;</button>
        </div>
        <div class="modal-body" style="padding: 1.5rem;">
            <form method="POST" data-validate>
                <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                
                <div class="form-group">
                    <label class="form-label">Title *</label>
                    <input type="text" name="title" class="form-control" placeholder="Brief description of the complaint" required>
                </div>
                
                <div class="form-group">
                    <label class="form-label">Category *</label>
                    <select name="category" class="form-control" required>
                        <option value="">Select Category</option>
                        <option value="Theft">Theft</option>
                        <option value="Assault">Assault</option>
                        <option value="Fraud">Fraud</option>
                        <option value="Vandalism">Vandalism</option>
                        <option value="Domestic Violence">Domestic Violence</option>
                        <option value="Traffic Violation">Traffic Violation</option>
                        <option value="Noise Complaint">Noise Complaint</option>
                        <option value="Other">Other</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label class="form-label">Description *</label>
                    <textarea name="description" class="form-control" rows="4" placeholder="Detailed description of the incident" required></textarea>
                </div>
                
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 1rem;">
                    <div class="form-group">
                        <label class="form-label">Location</label>
                        <input type="text" name="location" class="form-control" placeholder="Where did this occur?">
                    </div>
                    <div class="form-group">
                        <label class="form-label">Incident Date</label>
                        <input type="date" name="incident_date" class="form-control">
                    </div>
                </div>
                
                <div class="form-group">
                    <label class="form-label">Priority</label>
                    <select name="priority" class="form-control">
                        <option value="low">Low</option>
                        <option value="medium" selected>Medium</option>
                        <option value="high">High</option>
                        <option value="urgent">Urgent</option>
                    </select>
                </div>
                
                <div style="display: flex; gap: 1rem; justify-content: end; margin-top: 2rem;">
                    <button type="button" onclick="closeModal('complaintModal')" class="btn btn-outline">Cancel</button>
                    <button type="submit" name="submit_complaint" class="btn btn-primary">
                        <i class="fas fa-paper-plane"></i> Submit Complaint
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<style>
.modal {
    animation: fadeIn 0.3s ease-out;
}

.modal-content {
    animation: slideIn 0.3s ease-out;
    transform: translateY(0);
}

@keyframes slideIn {
    from {
        transform: translateY(-50px);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}
</style>

<?php require_once '../includes/footer.php'; ?>
