<?php
$page_title = 'Citizen Dashboard';
require_once '../config/database.php';
requireAuth('citizen');

try {
    $database = new Database();
    $conn = $database->getConnection();
    $citizen_id = $_SESSION['user_id'];
    
    // Get citizen's complaint statistics
    $stats = [];
    
    // Total complaints by this citizen
    $stmt = $conn->prepare("SELECT COUNT(*) as total FROM complaints WHERE citizen_id = ?");
    $stmt->execute([$citizen_id]);
    $stats['total_complaints'] = $stmt->fetch()['total'];
    
    // Complaints by status
    $stmt = $conn->prepare("SELECT status, COUNT(*) as count FROM complaints WHERE citizen_id = ? GROUP BY status");
    $stmt->execute([$citizen_id]);
    $complaint_status = [];
    while ($row = $stmt->fetch()) {
        $complaint_status[$row['status']] = $row['count'];
    }
    
    // Recent complaints
    $stmt = $conn->prepare("
        SELECT c.*, u.full_name as officer_name 
        FROM complaints c 
        LEFT JOIN users u ON c.assigned_officer_id = u.id 
        WHERE c.citizen_id = ? 
        ORDER BY c.created_at DESC 
        LIMIT 5
    ");
    $stmt->execute([$citizen_id]);
    $recent_complaints = $stmt->fetchAll();
    
} catch (PDOException $e) {
    $error = "Database error: " . $e->getMessage();
}

require_once '../includes/header.php';
?>

<div class="dashboard-header" style="margin-bottom: 2rem;">
    <h1 class="animate-fade-in">
        <i class="fas fa-tachometer-alt"></i>
        Dashboard
    </h1>
</div>

<!-- Statistics Cards -->
<div class="stats-grid" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 1.5rem; margin-bottom: 3rem;">
    
    <!-- Total Complaints -->
    <div class="card animate-fade-in" style="background: linear-gradient(135deg, var(--primary-color), #1d4ed8);">
        <div class="card-body" style="color: white;">
            <div style="display: flex; justify-content: space-between; align-items: center;">
                <div>
                    <h3 style="font-size: 2.5rem; margin: 0; font-weight: 700;">
                        <?php echo number_format($stats['total_complaints']); ?>
                    </h3>
                    <p style="margin: 0; opacity: 0.9;">Total Complaints</p>
                </div>
                <i class="fas fa-exclamation-triangle" style="font-size: 3rem; opacity: 0.3;"></i>
            </div>
        </div>
    </div>
    
    <!-- Under Review -->
    <div class="card animate-fade-in" style="background: linear-gradient(135deg, var(--warning-color), #d97706);">
        <div class="card-body" style="color: white;">
            <div style="display: flex; justify-content: space-between; align-items: center;">
                <div>
                    <h3 style="font-size: 2.5rem; margin: 0; font-weight: 700;">
                        <?php echo number_format($complaint_status['under_review'] ?? 0); ?>
                    </h3>
                    <p style="margin: 0; opacity: 0.9;">Under Review</p>
                </div>
                <i class="fas fa-clock" style="font-size: 3rem; opacity: 0.3;"></i>
            </div>
        </div>
    </div>
    
    <!-- Investigating -->
    <div class="card animate-fade-in" style="background: linear-gradient(135deg, var(--info-color), #0891b2);">
        <div class="card-body" style="color: white;">
            <div style="display: flex; justify-content: space-between; align-items: center;">
                <div>
                    <h3 style="font-size: 2.5rem; margin: 0; font-weight: 700;">
                        <?php echo number_format($complaint_status['investigating'] ?? 0); ?>
                    </h3>
                    <p style="margin: 0; opacity: 0.9;">Investigating</p>
                </div>
                <i class="fas fa-search" style="font-size: 3rem; opacity: 0.3;"></i>
            </div>
        </div>
    </div>
    
    <!-- Resolved -->
    <div class="card animate-fade-in" style="background: linear-gradient(135deg, var(--success-color), #059669);">
        <div class="card-body" style="color: white;">
            <div style="display: flex; justify-content: space-between; align-items: center;">
                <div>
                    <h3 style="font-size: 2.5rem; margin: 0; font-weight: 700;">
                        <?php echo number_format($complaint_status['resolved'] ?? 0); ?>
                    </h3>
                    <p style="margin: 0; opacity: 0.9;">Resolved</p>
                </div>
                <i class="fas fa-check-circle" style="font-size: 3rem; opacity: 0.3;"></i>
            </div>
        </div>
    </div>
</div>

<!-- Complaint Status Chart -->
<div class="card animate-fade-in" style="margin-bottom: 3rem;">
    <div class="card-header">
        <h3 class="card-title">
            <i class="fas fa-chart-pie"></i>
            Complaint Status Overview
        </h3>
    </div>
    <div class="card-body">
        <canvas id="complaintStatusChart" height="300"></canvas>
    </div>
</div>

<!-- Recent Complaints -->
<div class="card animate-fade-in">
    <div class="card-header">
        <h3 class="card-title">
            <i class="fas fa-clock"></i>
            Recent Complaints
        </h3>
        <a href="/citizen/complaints.php" class="btn btn-sm btn-outline">View All</a>
    </div>
    <div class="card-body" style="padding: 0;">
        <?php if (empty($recent_complaints)): ?>
            <div style="padding: 2rem; text-align: center; color: var(--text-muted);">
                <i class="fas fa-inbox" style="font-size: 3rem; margin-bottom: 1rem; opacity: 0.3;"></i>
                <h3>No complaints filed</h3>
                <p>You haven't filed any complaints yet.</p>
                <a href="/citizen/complaints.php" class="btn btn-primary" style="margin-top: 1rem;">
                    <i class="fas fa-plus"></i> File Complaint
                </a>
            </div>
        <?php else: ?>
            <div class="table-container">
                <table class="table">
                    <thead>
                        <tr>
                            <th>Complaint #</th>
                            <th>Title</th>
                            <th>Category</th>
                            <th>Status</th>
                            <th>Officer</th>
                            <th>Submitted</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($recent_complaints as $complaint): ?>
                            <tr>
                                <td>
                                    <strong><?php echo htmlspecialchars($complaint['complaint_number']); ?></strong>
                                </td>
                                <td>
                                    <?php echo htmlspecialchars(substr($complaint['title'], 0, 30)) . (strlen($complaint['title']) > 30 ? '...' : ''); ?>
                                </td>
                                <td><?php echo htmlspecialchars($complaint['category']); ?></td>
                                <td>
                                    <span class="badge badge-<?php 
                                        echo $complaint['status'] === 'resolved' ? 'success' : 
                                            ($complaint['status'] === 'investigating' ? 'info' : 
                                            ($complaint['status'] === 'under_review' ? 'warning' : 'secondary')); 
                                    ?>">
                                        <?php echo ucfirst(str_replace('_', ' ', $complaint['status'])); ?>
                                    </span>
                                </td>
                                <td>
                                    <?php echo $complaint['officer_name'] ? htmlspecialchars($complaint['officer_name']) : '<em>Not assigned</em>'; ?>
                                </td>
                                <td><?php echo date('M j, Y', strtotime($complaint['created_at'])); ?></td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        <?php endif; ?>
    </div>
</div>

<!-- Quick Actions -->
<div class="quick-actions" style="margin-top: 3rem;">
    <h3 style="margin-bottom: 1.5rem;">
        <i class="fas fa-bolt"></i>
        Quick Actions
    </h3>
    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem;">
        <a href="/citizen/file_complaint.php" class="btn btn-primary">
            <i class="fas fa-plus"></i>
            File Complaint
        </a>
        <a href="/citizen/complaints.php" class="btn btn-success">
            <i class="fas fa-list"></i>
            My Complaints
        </a>
        <a href="/citizen/track_case.php" class="btn btn-info">
            <i class="fas fa-search"></i>
            Track Case
        </a>
        <a href="/help.php" class="btn btn-secondary">
            <i class="fas fa-question-circle"></i>
            Help
        </a>
    </div>
</div>

<script>
// Complaint Status Chart
const complaintStatusCtx = document.getElementById('complaintStatusChart').getContext('2d');
new Chart(complaintStatusCtx, {
    type: 'doughnut',
    data: {
        labels: ['Submitted', 'Under Review', 'Investigating', 'Resolved', 'Closed'],
        datasets: [{
            data: [
                <?php echo $complaint_status['submitted'] ?? 0; ?>,
                <?php echo $complaint_status['under_review'] ?? 0; ?>,
                <?php echo $complaint_status['investigating'] ?? 0; ?>,
                <?php echo $complaint_status['resolved'] ?? 0; ?>,
                <?php echo $complaint_status['closed'] ?? 0; ?>
            ],
            backgroundColor: [
                'rgba(100, 116, 139, 0.8)',
                'rgba(245, 158, 11, 0.8)',
                'rgba(6, 182, 212, 0.8)',
                'rgba(16, 185, 129, 0.8)',
                'rgba(75, 85, 99, 0.8)'
            ],
            borderColor: [
                'rgba(100, 116, 139, 1)',
                'rgba(245, 158, 11, 1)',
                'rgba(6, 182, 212, 1)',
                'rgba(16, 185, 129, 1)',
                'rgba(75, 85, 99, 1)'
            ],
            borderWidth: 2
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                position: 'bottom',
                labels: {
                    color: 'rgba(248, 250, 252, 0.8)',
                    padding: 20
                }
            }
        }
    }
});
</script>

<style>
.stats-grid .card {
    transition: var(--transition);
}

.stats-grid .card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-xl);
}

.card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.quick-actions .btn {
    padding: 1rem;
    text-align: center;
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    align-items: center;
}

.quick-actions .btn i {
    font-size: 1.5rem;
}

@media (max-width: 768px) {
    .stats-grid {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)) !important;
    }
}
</style>

<?php require_once '../includes/footer.php'; ?>
