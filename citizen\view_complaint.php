<?php
$page_title = 'View Complaint';
require_once '../config/database.php';
requireAuth('citizen');

$complaint_id = (int)($_GET['id'] ?? 0);
$citizen_id = $_SESSION['user_id'];

if (!$complaint_id) {
    header('Location: /citizen/complaints.php');
    exit();
}

$database = new Database();
$conn = $database->getConnection();

try {
    $stmt = $conn->prepare("
        SELECT c.*, 
               u.full_name as officer_name,
               u.badge_number as officer_badge
        FROM complaints c 
        LEFT JOIN users u ON c.assigned_officer_id = u.id 
        WHERE c.id = ? AND c.citizen_id = ?
    ");
    $stmt->execute([$complaint_id, $citizen_id]);
    $complaint = $stmt->fetch();
    
    if (!$complaint) {
        $_SESSION['flash_message'] = 'Complaint not found.';
        $_SESSION['flash_type'] = 'error';
        header('Location: /citizen/complaints.php');
        exit();
    }
    
} catch (PDOException $e) {
    $error = 'Database error. Please try again later.';
}

require_once '../includes/header.php';
?>

<div class="page-header" style="margin-bottom: 2rem;">
    <h1><i class="fas fa-eye"></i> Complaint Details</h1>
    <a href="/citizen/complaints.php" class="btn btn-outline">
        <i class="fas fa-arrow-left"></i> Back to Complaints
    </a>
</div>

<div style="display: grid; grid-template-columns: 2fr 1fr; gap: 2rem;">
    <!-- Complaint Information -->
    <div>
        <div class="card" style="margin-bottom: 2rem;">
            <div class="card-header">
                <h3 class="card-title">Complaint Information</h3>
                <span class="badge badge-<?php 
                    echo $complaint['status'] === 'resolved' ? 'success' : 
                        ($complaint['status'] === 'investigating' ? 'info' : 
                        ($complaint['status'] === 'under_review' ? 'warning' : 'secondary')); 
                ?>">
                    <?php echo ucfirst(str_replace('_', ' ', $complaint['status'])); ?>
                </span>
            </div>
            <div class="card-body">
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 2rem;">
                    <div>
                        <h4>Complaint Number</h4>
                        <p style="font-size: 1.2rem; font-weight: 600; color: var(--primary-color);">
                            <?php echo htmlspecialchars($complaint['complaint_number']); ?>
                        </p>
                        
                        <h4>Title</h4>
                        <p><?php echo htmlspecialchars($complaint['title']); ?></p>
                        
                        <h4>Category</h4>
                        <p><?php echo htmlspecialchars($complaint['category']); ?></p>
                        
                        <h4>Priority</h4>
                        <span class="badge badge-<?php 
                            echo $complaint['priority'] === 'urgent' ? 'danger' : 
                                ($complaint['priority'] === 'high' ? 'warning' : 
                                ($complaint['priority'] === 'medium' ? 'info' : 'secondary')); 
                        ?>">
                            <?php echo ucfirst($complaint['priority']); ?>
                        </span>
                    </div>
                    <div>
                        <?php if ($complaint['incident_date']): ?>
                        <h4>Incident Date</h4>
                        <p><?php echo date('F j, Y', strtotime($complaint['incident_date'])); ?></p>
                        <?php endif; ?>
                        
                        <?php if ($complaint['location']): ?>
                        <h4>Location</h4>
                        <p><?php echo htmlspecialchars($complaint['location']); ?></p>
                        <?php endif; ?>
                        
                        <h4>Submitted</h4>
                        <p><?php echo date('F j, Y g:i A', strtotime($complaint['created_at'])); ?></p>
                        
                        <h4>Assigned Officer</h4>
                        <p>
                            <?php if ($complaint['officer_name']): ?>
                                <?php echo htmlspecialchars($complaint['officer_name']); ?>
                                <?php if ($complaint['officer_badge']): ?>
                                    <small>(<?php echo htmlspecialchars($complaint['officer_badge']); ?>)</small>
                                <?php endif; ?>
                            <?php else: ?>
                                <em>Not assigned yet</em>
                            <?php endif; ?>
                        </p>
                    </div>
                </div>
                
                <h4 style="margin-top: 2rem;">Description</h4>
                <p style="background: var(--bg-tertiary); padding: 1rem; border-radius: var(--border-radius); line-height: 1.6;">
                    <?php echo nl2br(htmlspecialchars($complaint['description'])); ?>
                </p>
                
                <?php if ($complaint['response']): ?>
                <h4 style="margin-top: 2rem;">Official Response</h4>
                <div style="background: var(--bg-secondary); padding: 1rem; border-radius: var(--border-radius); border-left: 4px solid var(--primary-color);">
                    <p style="margin: 0; line-height: 1.6;">
                        <?php echo nl2br(htmlspecialchars($complaint['response'])); ?>
                    </p>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
    
    <!-- Status Timeline -->
    <div>
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">Status Timeline</h3>
            </div>
            <div class="card-body">
                <div class="timeline">
                    <!-- Complaint Submitted -->
                    <div class="timeline-item">
                        <div class="timeline-marker" style="background: var(--primary-color);"></div>
                        <div class="timeline-content">
                            <h5>Complaint Submitted</h5>
                            <p style="color: var(--text-muted); font-size: 0.875rem;">
                                <?php echo date('M j, Y g:i A', strtotime($complaint['created_at'])); ?>
                            </p>
                            <p>Your complaint has been received and assigned reference number <?php echo htmlspecialchars($complaint['complaint_number']); ?></p>
                        </div>
                    </div>
                    
                    <!-- Status Updates -->
                    <?php if ($complaint['status'] !== 'submitted'): ?>
                    <div class="timeline-item">
                        <div class="timeline-marker" style="background: var(--warning-color);"></div>
                        <div class="timeline-content">
                            <h5>Under Review</h5>
                            <p style="color: var(--text-muted); font-size: 0.875rem;">
                                <?php echo date('M j, Y', strtotime($complaint['updated_at'])); ?>
                            </p>
                            <p>Your complaint is being reviewed by our team.</p>
                        </div>
                    </div>
                    <?php endif; ?>
                    
                    <?php if ($complaint['assigned_officer_id']): ?>
                    <div class="timeline-item">
                        <div class="timeline-marker" style="background: var(--info-color);"></div>
                        <div class="timeline-content">
                            <h5>Officer Assigned</h5>
                            <p style="color: var(--text-muted); font-size: 0.875rem;">
                                <?php echo date('M j, Y', strtotime($complaint['updated_at'])); ?>
                            </p>
                            <p>Assigned to <?php echo htmlspecialchars($complaint['officer_name']); ?></p>
                        </div>
                    </div>
                    <?php endif; ?>
                    
                    <?php if ($complaint['status'] === 'investigating'): ?>
                    <div class="timeline-item">
                        <div class="timeline-marker" style="background: var(--info-color);"></div>
                        <div class="timeline-content">
                            <h5>Investigation Started</h5>
                            <p style="color: var(--text-muted); font-size: 0.875rem;">
                                <?php echo date('M j, Y', strtotime($complaint['updated_at'])); ?>
                            </p>
                            <p>Investigation is currently in progress.</p>
                        </div>
                    </div>
                    <?php endif; ?>
                    
                    <?php if ($complaint['status'] === 'resolved'): ?>
                    <div class="timeline-item">
                        <div class="timeline-marker" style="background: var(--success-color);"></div>
                        <div class="timeline-content">
                            <h5>Complaint Resolved</h5>
                            <p style="color: var(--text-muted); font-size: 0.875rem;">
                                <?php echo date('M j, Y', strtotime($complaint['updated_at'])); ?>
                            </p>
                            <p>Your complaint has been resolved.</p>
                        </div>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
        
        <!-- Contact Information -->
        <div class="card" style="margin-top: 2rem;">
            <div class="card-header">
                <h3 class="card-title">Need Help?</h3>
            </div>
            <div class="card-body">
                <p style="margin-bottom: 1rem;">If you have questions about your complaint, you can:</p>
                <ul style="list-style: none; padding: 0;">
                    <li style="margin-bottom: 0.5rem;">
                        <i class="fas fa-phone" style="color: var(--primary-color); margin-right: 0.5rem;"></i>
                        Call: +266-2234-5678
                    </li>
                    <li style="margin-bottom: 0.5rem;">
                        <i class="fas fa-envelope" style="color: var(--primary-color); margin-right: 0.5rem;"></i>
                        Email: <EMAIL>
                    </li>
                    <li>
                        <i class="fas fa-map-marker-alt" style="color: var(--primary-color); margin-right: 0.5rem;"></i>
                        Visit: Police Headquarters, Maseru
                    </li>
                </ul>
                <p style="margin-top: 1rem; font-size: 0.875rem; color: var(--text-muted);">
                    Reference your complaint number: <strong><?php echo htmlspecialchars($complaint['complaint_number']); ?></strong>
                </p>
            </div>
        </div>
    </div>
</div>

<style>
.page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.timeline {
    position: relative;
    padding-left: 2rem;
}

.timeline::before {
    content: '';
    position: absolute;
    left: 0.5rem;
    top: 0;
    bottom: 0;
    width: 2px;
    background: var(--border-color);
}

.timeline-item {
    position: relative;
    margin-bottom: 2rem;
}

.timeline-marker {
    position: absolute;
    left: -2rem;
    top: 0.25rem;
    width: 1rem;
    height: 1rem;
    border-radius: 50%;
    border: 2px solid var(--bg-card);
}

.timeline-content h5 {
    margin: 0 0 0.25rem 0;
    font-size: 1rem;
}

.timeline-content p {
    margin: 0.25rem 0;
}

h4 {
    margin: 1rem 0 0.5rem 0;
    font-size: 0.875rem;
    font-weight: 600;
    color: var(--text-secondary);
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

h4 + p {
    margin-top: 0;
}
</style>

<?php require_once '../includes/footer.php'; ?>
