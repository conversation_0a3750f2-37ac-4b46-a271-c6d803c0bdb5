-- Criminal Record Management System Database Setup
-- Create database
CREATE DATABASE IF NOT EXISTS crms_lesotho;
USE crms_lesotho;

-- Users table (Ad<PERSON>, Police Officers, Citizens)
CREATE TABLE users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHA<PERSON>(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    full_name VARCHAR(100) NOT NULL,
    role ENUM('admin', 'police', 'citizen') NOT NULL,
    phone VARCHAR(20),
    address TEXT,
    badge_number VARCHAR(20) NULL, -- For police officers
    status ENUM('active', 'inactive') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Criminal records table
CREATE TABLE criminal_records (
    id INT AUTO_INCREMENT PRIMARY KEY,
    case_number VARCHAR(50) UNIQUE NOT NULL,
    title VARCHAR(200) NOT NULL,
    description TEXT NOT NULL,
    crime_type VARCHAR(100) NOT NULL,
    suspect_name VARCHAR(100),
    suspect_id_number VARCHAR(20),
    suspect_address TEXT,
    victim_name VARCHAR(100),
    victim_contact VARCHAR(50),
    incident_date DATE NOT NULL,
    incident_location VARCHAR(200) NOT NULL,
    status ENUM('ongoing', 'solved', 'paused', 'closed') DEFAULT 'ongoing',
    priority ENUM('low', 'medium', 'high', 'critical') DEFAULT 'medium',
    assigned_officer_id INT,
    created_by INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (assigned_officer_id) REFERENCES users(id) ON DELETE SET NULL,
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE CASCADE
);

-- Complaints table (for citizens)
CREATE TABLE complaints (
    id INT AUTO_INCREMENT PRIMARY KEY,
    complaint_number VARCHAR(50) UNIQUE NOT NULL,
    citizen_id INT NOT NULL,
    title VARCHAR(200) NOT NULL,
    description TEXT NOT NULL,
    category VARCHAR(100) NOT NULL,
    location VARCHAR(200),
    incident_date DATE,
    status ENUM('submitted', 'under_review', 'investigating', 'resolved', 'closed') DEFAULT 'submitted',
    priority ENUM('low', 'medium', 'high', 'urgent') DEFAULT 'medium',
    assigned_officer_id INT,
    response TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (citizen_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (assigned_officer_id) REFERENCES users(id) ON DELETE SET NULL
);

-- Hearings table
CREATE TABLE hearings (
    id INT AUTO_INCREMENT PRIMARY KEY,
    case_id INT NOT NULL,
    hearing_date DATETIME NOT NULL,
    location VARCHAR(200) NOT NULL,
    judge_name VARCHAR(100),
    description TEXT,
    status ENUM('scheduled', 'completed', 'postponed', 'cancelled') DEFAULT 'scheduled',
    created_by INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (case_id) REFERENCES criminal_records(id) ON DELETE CASCADE,
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE CASCADE
);

-- Case updates/notes table
CREATE TABLE case_updates (
    id INT AUTO_INCREMENT PRIMARY KEY,
    case_id INT NOT NULL,
    update_text TEXT NOT NULL,
    updated_by INT NOT NULL,
    update_type ENUM('note', 'evidence', 'witness', 'status_change') DEFAULT 'note',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (case_id) REFERENCES criminal_records(id) ON DELETE CASCADE,
    FOREIGN KEY (updated_by) REFERENCES users(id) ON DELETE CASCADE
);

-- Insert default admin user
INSERT INTO users (username, email, password, full_name, role, phone, address) VALUES 
('admin', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'System Administrator', 'admin', '+266-2234-5678', 'Police Headquarters, Maseru');

-- Insert sample police officer
INSERT INTO users (username, email, password, full_name, role, phone, address, badge_number) VALUES 
('officer1', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Officer John Mokoena', 'police', '+266-5678-9012', 'Maseru Police Station', 'LPF001');

-- Insert sample citizen
INSERT INTO users (username, email, password, full_name, role, phone, address) VALUES 
('citizen1', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Mary Lebohang', 'citizen', '+266-9876-5432', 'Ha Abia, Maseru');
