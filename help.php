<?php
$page_title = 'Help';
require_once 'config/database.php';

require_once 'includes/header.php';
?>

<div class="page-header" style="margin-bottom: 2rem;">
    <h1><i class="fas fa-question-circle"></i> Help & Support</h1>
</div>

<div style="display: grid; grid-template-columns: 1fr 300px; gap: 2rem;">
    
    <!-- Main Content -->
    <div>
        
        <!-- Getting Started -->
        <div class="card" style="margin-bottom: 2rem;">
            <div class="card-header">
                <h3 class="card-title">Getting Started</h3>
            </div>
            <div class="card-body">
                <h4>System Overview</h4>
                <p>The Criminal Record Management System (CRMS) is designed for the Lesotho Police Force to manage criminal records, complaints, and investigations efficiently.</p>
                
                <h4>User Roles</h4>
                <ul>
                    <li><strong>Administrator:</strong> Full system access, user management, case oversight</li>
                    <li><strong>Police Officer:</strong> Case management, investigation tracking, reports</li>
                    <li><strong>Citizen:</strong> File complaints, track complaint status</li>
                </ul>
            </div>
        </div>
        
        <!-- For Administrators -->
        <?php if (isLoggedIn() && hasRole('admin')): ?>
        <div class="card" style="margin-bottom: 2rem;">
            <div class="card-header">
                <h3 class="card-title">Administrator Guide</h3>
            </div>
            <div class="card-body">
                <h4>User Management</h4>
                <ul>
                    <li>Add new users (police officers, citizens)</li>
                    <li>Edit user information and roles</li>
                    <li>Activate/deactivate user accounts</li>
                    <li>Assign badge numbers to police officers</li>
                </ul>
                
                <h4>Case Management</h4>
                <ul>
                    <li>View all criminal records in the system</li>
                    <li>Create new cases and assign to officers</li>
                    <li>Update case status and priority</li>
                    <li>Search cases by number, title, or suspect</li>
                </ul>
                
                <h4>Reports</h4>
                <ul>
                    <li>Generate CSV reports for cases, users, and complaints</li>
                    <li>View system analytics and statistics</li>
                    <li>Download data for specific date ranges</li>
                </ul>
            </div>
        </div>
        <?php endif; ?>
        
        <!-- For Police Officers -->
        <?php if (isLoggedIn() && hasRole('police')): ?>
        <div class="card" style="margin-bottom: 2rem;">
            <div class="card-header">
                <h3 class="card-title">Police Officer Guide</h3>
            </div>
            <div class="card-body">
                <h4>Managing Cases</h4>
                <ul>
                    <li>File new criminal records</li>
                    <li>Update case details and status (Ongoing, Solved, Paused)</li>
                    <li>View cases assigned to you</li>
                    <li>Search and filter your cases</li>
                </ul>
                
                <h4>Case Status Options</h4>
                <ul>
                    <li><strong>Ongoing:</strong> Case is actively being investigated</li>
                    <li><strong>Solved:</strong> Case has been resolved</li>
                    <li><strong>Paused:</strong> Investigation temporarily suspended</li>
                    <li><strong>Closed:</strong> Case officially closed</li>
                </ul>
                
                <h4>Reports</h4>
                <ul>
                    <li>Generate reports for your assigned cases</li>
                    <li>Download case data for specific periods</li>
                    <li>View your case statistics and analytics</li>
                </ul>
            </div>
        </div>
        <?php endif; ?>
        
        <!-- For Citizens -->
        <?php if (isLoggedIn() && hasRole('citizen')): ?>
        <div class="card" style="margin-bottom: 2rem;">
            <div class="card-header">
                <h3 class="card-title">Citizen Guide</h3>
            </div>
            <div class="card-body">
                <h4>Filing Complaints</h4>
                <ul>
                    <li>Click "File Complaint" to submit a new complaint</li>
                    <li>Provide detailed description of the incident</li>
                    <li>Select appropriate category and priority</li>
                    <li>Include location and date if known</li>
                </ul>
                
                <h4>Tracking Complaints</h4>
                <ul>
                    <li>View all your submitted complaints</li>
                    <li>Check complaint status and progress</li>
                    <li>See assigned officer information</li>
                    <li>Read official responses</li>
                </ul>
                
                <h4>Complaint Status</h4>
                <ul>
                    <li><strong>Submitted:</strong> Complaint received</li>
                    <li><strong>Under Review:</strong> Being evaluated</li>
                    <li><strong>Investigating:</strong> Active investigation</li>
                    <li><strong>Resolved:</strong> Complaint addressed</li>
                </ul>
            </div>
        </div>
        <?php endif; ?>
        
        <!-- Common Features -->
        <div class="card" style="margin-bottom: 2rem;">
            <div class="card-header">
                <h3 class="card-title">Common Features</h3>
            </div>
            <div class="card-body">
                <h4>Navigation</h4>
                <ul>
                    <li>Use the top navigation menu to access different sections</li>
                    <li>Dashboard provides overview and quick actions</li>
                    <li>User menu (top right) for profile and logout</li>
                </ul>
                
                <h4>Search & Filters</h4>
                <ul>
                    <li>Use search boxes to find specific records</li>
                    <li>Apply filters to narrow down results</li>
                    <li>Click column headers to sort tables</li>
                </ul>
                
                <h4>Profile Management</h4>
                <ul>
                    <li>Update your personal information</li>
                    <li>Change your password</li>
                    <li>View account details</li>
                </ul>
            </div>
        </div>
        
        <!-- Troubleshooting -->
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">Troubleshooting</h3>
            </div>
            <div class="card-body">
                <h4>Common Issues</h4>
                <ul>
                    <li><strong>Can't login:</strong> Check username/password, ensure account is active</li>
                    <li><strong>Page not loading:</strong> Refresh browser, check internet connection</li>
                    <li><strong>Form errors:</strong> Ensure all required fields are filled</li>
                    <li><strong>Access denied:</strong> Contact administrator for role permissions</li>
                </ul>
                
                <h4>Browser Requirements</h4>
                <ul>
                    <li>Modern web browser (Chrome, Firefox, Safari, Edge)</li>
                    <li>JavaScript enabled</li>
                    <li>Cookies enabled</li>
                </ul>
            </div>
        </div>
    </div>
    
    <!-- Sidebar -->
    <div>
        
        <!-- Quick Links -->
        <div class="card" style="margin-bottom: 2rem;">
            <div class="card-header">
                <h3 class="card-title">Quick Links</h3>
            </div>
            <div class="card-body">
                <?php if (isLoggedIn()): ?>
                    <?php if (hasRole('admin')): ?>
                        <a href="/admin/dashboard.php" class="btn btn-outline w-full" style="margin-bottom: 0.5rem;">
                            <i class="fas fa-tachometer-alt"></i> Admin Dashboard
                        </a>
                        <a href="/admin/manage_users.php" class="btn btn-outline w-full" style="margin-bottom: 0.5rem;">
                            <i class="fas fa-users"></i> Manage Users
                        </a>
                        <a href="/admin/manage_cases.php" class="btn btn-outline w-full" style="margin-bottom: 0.5rem;">
                            <i class="fas fa-folder-open"></i> Manage Cases
                        </a>
                    <?php elseif (hasRole('police')): ?>
                        <a href="/police/dashboard.php" class="btn btn-outline w-full" style="margin-bottom: 0.5rem;">
                            <i class="fas fa-tachometer-alt"></i> Dashboard
                        </a>
                        <a href="/police/cases.php" class="btn btn-outline w-full" style="margin-bottom: 0.5rem;">
                            <i class="fas fa-folder-open"></i> My Cases
                        </a>
                        <a href="/police/add_case.php" class="btn btn-outline w-full" style="margin-bottom: 0.5rem;">
                            <i class="fas fa-plus"></i> New Case
                        </a>
                    <?php elseif (hasRole('citizen')): ?>
                        <a href="/citizen/dashboard.php" class="btn btn-outline w-full" style="margin-bottom: 0.5rem;">
                            <i class="fas fa-tachometer-alt"></i> Dashboard
                        </a>
                        <a href="/citizen/complaints.php" class="btn btn-outline w-full" style="margin-bottom: 0.5rem;">
                            <i class="fas fa-exclamation-triangle"></i> My Complaints
                        </a>
                    <?php endif; ?>
                    <a href="/profile.php" class="btn btn-outline w-full">
                        <i class="fas fa-user"></i> My Profile
                    </a>
                <?php else: ?>
                    <a href="/auth/login.php" class="btn btn-primary w-full" style="margin-bottom: 0.5rem;">
                        <i class="fas fa-sign-in-alt"></i> Login
                    </a>
                    <a href="/auth/register.php" class="btn btn-outline w-full">
                        <i class="fas fa-user-plus"></i> Register
                    </a>
                <?php endif; ?>
            </div>
        </div>
        
        <!-- Contact Information -->
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">Contact Support</h3>
            </div>
            <div class="card-body">
                <div style="margin-bottom: 1rem;">
                    <h4>Lesotho Police Force</h4>
                    <p style="margin: 0;">Ministry of Defense Lesotho</p>
                </div>
                
                <div style="margin-bottom: 1rem;">
                    <i class="fas fa-phone" style="color: var(--primary-color); margin-right: 0.5rem;"></i>
                    <strong>Phone:</strong><br>
                    +266-2234-5678
                </div>
                
                <div style="margin-bottom: 1rem;">
                    <i class="fas fa-envelope" style="color: var(--primary-color); margin-right: 0.5rem;"></i>
                    <strong>Email:</strong><br>
                    <EMAIL>
                </div>
                
                <div>
                    <i class="fas fa-map-marker-alt" style="color: var(--primary-color); margin-right: 0.5rem;"></i>
                    <strong>Address:</strong><br>
                    Police Headquarters<br>
                    Maseru, Lesotho
                </div>
            </div>
        </div>
    </div>
</div>

<style>
h4 {
    margin: 1.5rem 0 0.5rem 0;
    font-size: 1rem;
    font-weight: 600;
    color: var(--text-primary);
}

h4:first-child {
    margin-top: 0;
}

ul {
    margin: 0.5rem 0 1rem 1rem;
}

li {
    margin-bottom: 0.25rem;
}

@media (max-width: 768px) {
    .page-content > div:first-child {
        grid-template-columns: 1fr !important;
    }
}
</style>

<?php require_once 'includes/footer.php'; ?>
