<?php
require_once __DIR__ . '/../config/database.php';
startSession();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo isset($page_title) ? $page_title . ' - ' : ''; ?>CRMS Lesotho</title>
    
    <!-- Favicon -->
    <link rel="icon" type="image/png" href="/assets/police-force(3).png">
    
    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Font Awesome Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="/assets/css/style.css">
    
    <!-- Chart.js for analytics -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <!-- Custom CSS for specific pages -->
    <?php if (isset($additional_css)): ?>
        <?php foreach ($additional_css as $css): ?>
            <link rel="stylesheet" href="<?php echo $css; ?>">
        <?php endforeach; ?>
    <?php endif; ?>
</head>
<body>
    <header class="header">
        <div class="container">
            <div class="header-content">
                <a href="/index.php" class="logo">
                    <img src="/assets/police-force(3).png" alt="LPF Logo">
                    <span>CRMS Lesotho</span>
                </a>
                
                <?php if (isLoggedIn()): ?>
                    <div class="user-menu">
                        <button class="user-toggle">
                            <i class="fas fa-user-circle"></i>
                            <span><?php echo htmlspecialchars($_SESSION['full_name']); ?></span>
                            <i class="fas fa-chevron-down"></i>
                        </button>
                        <div class="user-dropdown">
                            <a href="/profile.php">
                                <i class="fas fa-user"></i>
                                Profile
                            </a>
                            <a href="/settings.php">
                                <i class="fas fa-cog"></i>
                                Settings
                            </a>
                            <hr style="margin: 0.5rem 0; border: none; border-top: 1px solid var(--border-color);">
                            <a href="/auth/logout.php">
                                <i class="fas fa-sign-out-alt"></i>
                                Logout
                            </a>
                        </div>
                    </div>
                <?php else: ?>
                    <div class="auth-buttons">
                        <a href="/auth/login.php" class="btn btn-outline">
                            <i class="fas fa-sign-in-alt"></i>
                            Login
                        </a>
                        <a href="/auth/register.php" class="btn btn-primary">
                            <i class="fas fa-user-plus"></i>
                            Register
                        </a>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </header>

    <?php if (isLoggedIn()): ?>
        <nav class="nav">
            <div class="container">
                <ul class="nav-list">
                    <?php
                    $current_page = basename($_SERVER['PHP_SELF']);
                    $role = $_SESSION['role'];
                    
                    // Common navigation items
                    $nav_items = [];
                    
                    if ($role === 'admin') {
                        $nav_items = [
                            ['href' => '/admin/dashboard.php', 'icon' => 'fas fa-tachometer-alt', 'text' => 'Dashboard'],
                            ['href' => '/admin/manage_users.php', 'icon' => 'fas fa-users', 'text' => 'Manage Users'],
                            ['href' => '/admin/manage_cases.php', 'icon' => 'fas fa-folder-open', 'text' => 'Manage Cases'],
                            ['href' => '/admin/reports.php', 'icon' => 'fas fa-chart-bar', 'text' => 'Reports'],
                        ];
                    } elseif ($role === 'police') {
                        $nav_items = [
                            ['href' => '/police/dashboard.php', 'icon' => 'fas fa-tachometer-alt', 'text' => 'Dashboard'],
                            ['href' => '/police/cases.php', 'icon' => 'fas fa-folder-open', 'text' => 'Cases'],
                            ['href' => '/police/reports.php', 'icon' => 'fas fa-chart-bar', 'text' => 'Reports'],
                        ];
                    } elseif ($role === 'citizen') {
                        $nav_items = [
                            ['href' => '/citizen/dashboard.php', 'icon' => 'fas fa-tachometer-alt', 'text' => 'Dashboard'],
                            ['href' => '/citizen/complaints.php', 'icon' => 'fas fa-exclamation-triangle', 'text' => 'Complaints'],
                        ];
                    }
                    
                    foreach ($nav_items as $item):
                        $is_active = (basename($item['href']) === $current_page) ? 'active' : '';
                    ?>
                        <li class="nav-item">
                            <a href="<?php echo $item['href']; ?>" class="<?php echo $is_active; ?>">
                                <i class="<?php echo $item['icon']; ?>"></i>
                                <?php echo $item['text']; ?>
                            </a>
                        </li>
                    <?php endforeach; ?>
                </ul>
            </div>
        </nav>
    <?php endif; ?>

    <main class="main-content">
        <div class="container">
            <?php
            // Display flash messages
            if (isset($_SESSION['flash_message'])):
                $message = $_SESSION['flash_message'];
                $type = $_SESSION['flash_type'] ?? 'info';
                unset($_SESSION['flash_message'], $_SESSION['flash_type']);
            ?>
                <div class="alert alert-<?php echo $type; ?> animate-fade-in" style="margin-bottom: 2rem;">
                    <div style="display: flex; justify-content: space-between; align-items: center;">
                        <span><?php echo htmlspecialchars($message); ?></span>
                        <button onclick="this.parentElement.parentElement.style.display='none'" style="background: none; border: none; color: inherit; cursor: pointer; font-size: 1.2rem;">&times;</button>
                    </div>
                </div>
                <style>
                    .alert {
                        padding: 1rem;
                        border-radius: var(--border-radius);
                        border: 1px solid;
                        margin-bottom: 1rem;
                    }
                    .alert-success {
                        background: rgba(16, 185, 129, 0.1);
                        border-color: var(--success-color);
                        color: var(--success-color);
                    }
                    .alert-error {
                        background: rgba(239, 68, 68, 0.1);
                        border-color: var(--danger-color);
                        color: var(--danger-color);
                    }
                    .alert-warning {
                        background: rgba(245, 158, 11, 0.1);
                        border-color: var(--warning-color);
                        color: var(--warning-color);
                    }
                    .alert-info {
                        background: rgba(6, 182, 212, 0.1);
                        border-color: var(--info-color);
                        color: var(--info-color);
                    }
                </style>
            <?php endif; ?>
