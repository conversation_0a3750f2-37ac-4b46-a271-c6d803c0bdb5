<?php
$page_title = 'Home';
require_once 'includes/header.php';

// Redirect logged-in users to their respective dashboards
if (isLoggedIn()) {
    $role = $_SESSION['role'];
    switch ($role) {
        case 'admin':
            header('Location: /admin/dashboard.php');
            break;
        case 'police':
            header('Location: /police/dashboard.php');
            break;
        case 'citizen':
            header('Location: /citizen/dashboard.php');
            break;
    }
    exit();
}
?>

<div class="hero-section" style="text-align: center; padding: 4rem 0; background: linear-gradient(135deg, var(--bg-secondary) 0%, var(--bg-tertiary) 100%); border-radius: var(--border-radius-lg); margin-bottom: 3rem;">
    <div class="hero-content animate-fade-in">
        <img src="/assets/police-force(3).png" alt="Lesotho Police Force" style="width: 120px; height: 120px; margin-bottom: 2rem; border-radius: 50%; box-shadow: var(--shadow-lg);">
        <h1 style="font-size: 3rem; margin-bottom: 1rem; background: linear-gradient(135deg, var(--primary-color), var(--accent-color)); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text;">
            Criminal Record Management System
        </h1>
        <h2 style="font-size: 1.5rem; color: var(--text-secondary); margin-bottom: 2rem; font-weight: 400;">
            Lesotho Police Force
        </h2>
        <p style="font-size: 1.1rem; color: var(--text-muted); max-width: 600px; margin: 0 auto 3rem; line-height: 1.8;">
            Streamlining law enforcement operations for the Ministry of Defense Lesotho. 
            Secure, efficient, and comprehensive criminal record management for public safety and national security.
        </p>
        
        <div class="cta-buttons" style="display: flex; gap: 1rem; justify-content: center; flex-wrap: wrap;">
            <a href="/auth/login.php" class="btn btn-primary btn-lg">
                <i class="fas fa-sign-in-alt"></i>
                Login to System
            </a>
            <a href="/auth/register.php" class="btn btn-outline btn-lg">
                <i class="fas fa-user-plus"></i>
                Register Account
            </a>
        </div>
    </div>
</div>

<div class="features-section">
    <h2 style="text-align: center; margin-bottom: 3rem; font-size: 2.5rem;">System Features</h2>
    
    <div class="features-grid" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 2rem; margin-bottom: 4rem;">
        
        <!-- Admin Features -->
        <div class="card animate-fade-in">
            <div class="card-header" style="background: linear-gradient(135deg, var(--danger-color), #dc2626);">
                <h3 class="card-title">
                    <i class="fas fa-user-shield"></i>
                    Administrator Portal
                </h3>
            </div>
            <div class="card-body">
                <ul style="list-style: none; padding: 0;">
                    <li style="padding: 0.5rem 0; border-bottom: 1px solid var(--border-color);"><i class="fas fa-check text-success"></i> System Analytics Dashboard</li>
                    <li style="padding: 0.5rem 0; border-bottom: 1px solid var(--border-color);"><i class="fas fa-check text-success"></i> User Management</li>
                    <li style="padding: 0.5rem 0; border-bottom: 1px solid var(--border-color);"><i class="fas fa-check text-success"></i> Criminal Records Management</li>
                    <li style="padding: 0.5rem 0; border-bottom: 1px solid var(--border-color);"><i class="fas fa-check text-success"></i> Report Generation</li>
                    <li style="padding: 0.5rem 0;"><i class="fas fa-check text-success"></i> System Configuration</li>
                </ul>
            </div>
        </div>
        
        <!-- Police Features -->
        <div class="card animate-fade-in">
            <div class="card-header" style="background: linear-gradient(135deg, var(--primary-color), #1d4ed8);">
                <h3 class="card-title">
                    <i class="fas fa-badge"></i>
                    Police Officer Portal
                </h3>
            </div>
            <div class="card-body">
                <ul style="list-style: none; padding: 0;">
                    <li style="padding: 0.5rem 0; border-bottom: 1px solid var(--border-color);"><i class="fas fa-check text-success"></i> Case Management Dashboard</li>
                    <li style="padding: 0.5rem 0; border-bottom: 1px solid var(--border-color);"><i class="fas fa-check text-success"></i> File New Criminal Records</li>
                    <li style="padding: 0.5rem 0; border-bottom: 1px solid var(--border-color);"><i class="fas fa-check text-success"></i> Update Case Status</li>
                    <li style="padding: 0.5rem 0; border-bottom: 1px solid var(--border-color);"><i class="fas fa-check text-success"></i> Generate Reports</li>
                    <li style="padding: 0.5rem 0;"><i class="fas fa-check text-success"></i> Case Analytics</li>
                </ul>
            </div>
        </div>
        
        <!-- Citizen Features -->
        <div class="card animate-fade-in">
            <div class="card-header" style="background: linear-gradient(135deg, var(--success-color), #059669);">
                <h3 class="card-title">
                    <i class="fas fa-users"></i>
                    Citizen Portal
                </h3>
            </div>
            <div class="card-body">
                <ul style="list-style: none; padding: 0;">
                    <li style="padding: 0.5rem 0; border-bottom: 1px solid var(--border-color);"><i class="fas fa-check text-success"></i> Track Case Progress</li>
                    <li style="padding: 0.5rem 0; border-bottom: 1px solid var(--border-color);"><i class="fas fa-check text-success"></i> File Complaints</li>
                    <li style="padding: 0.5rem 0; border-bottom: 1px solid var(--border-color);"><i class="fas fa-check text-success"></i> View Complaint Status</li>
                    <li style="padding: 0.5rem 0; border-bottom: 1px solid var(--border-color);"><i class="fas fa-check text-success"></i> Secure Communication</li>
                    <li style="padding: 0.5rem 0;"><i class="fas fa-check text-success"></i> Personal Dashboard</li>
                </ul>
            </div>
        </div>
    </div>
    
    <!-- Security Features -->
    <div class="security-section">
        <h3 style="text-align: center; margin-bottom: 2rem; font-size: 2rem;">Security & Compliance</h3>
        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 1.5rem;">
            
            <div class="security-feature" style="text-align: center; padding: 2rem; background: var(--bg-card); border-radius: var(--border-radius); border: 1px solid var(--border-color);">
                <i class="fas fa-shield-alt" style="font-size: 3rem; color: var(--success-color); margin-bottom: 1rem;"></i>
                <h4>Role-Based Access</h4>
                <p style="color: var(--text-muted);">Multi-level security with role-based permissions</p>
            </div>
            
            <div class="security-feature" style="text-align: center; padding: 2rem; background: var(--bg-card); border-radius: var(--border-radius); border: 1px solid var(--border-color);">
                <i class="fas fa-lock" style="font-size: 3rem; color: var(--warning-color); margin-bottom: 1rem;"></i>
                <h4>Data Encryption</h4>
                <p style="color: var(--text-muted);">End-to-end encryption for sensitive information</p>
            </div>
            
            <div class="security-feature" style="text-align: center; padding: 2rem; background: var(--bg-card); border-radius: var(--border-radius); border: 1px solid var(--border-color);">
                <i class="fas fa-history" style="font-size: 3rem; color: var(--info-color); margin-bottom: 1rem;"></i>
                <h4>Audit Trail</h4>
                <p style="color: var(--text-muted);">Complete activity logging and audit trails</p>
            </div>
            
            <div class="security-feature" style="text-align: center; padding: 2rem; background: var(--bg-card); border-radius: var(--border-radius); border: 1px solid var(--border-color);">
                <i class="fas fa-certificate" style="font-size: 3rem; color: var(--primary-color); margin-bottom: 1rem;"></i>
                <h4>Compliance</h4>
                <p style="color: var(--text-muted);">Meets government security standards</p>
            </div>
        </div>
    </div>
</div>

<style>
.text-success {
    color: var(--success-color) !important;
}

.features-grid .card {
    transition: var(--transition);
}

.features-grid .card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-xl);
}

.security-feature {
    transition: var(--transition);
}

.security-feature:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-lg);
}

.hero-content {
    animation-delay: 0.2s;
}

.features-grid .card:nth-child(1) {
    animation-delay: 0.4s;
}

.features-grid .card:nth-child(2) {
    animation-delay: 0.6s;
}

.features-grid .card:nth-child(3) {
    animation-delay: 0.8s;
}

@media (max-width: 768px) {
    .hero-section h1 {
        font-size: 2rem !important;
    }
    
    .hero-section h2 {
        font-size: 1.2rem !important;
    }
    
    .cta-buttons {
        flex-direction: column !important;
        align-items: center;
    }
    
    .cta-buttons .btn {
        width: 100%;
        max-width: 300px;
    }
}
</style>

<?php require_once 'includes/footer.php'; ?>
