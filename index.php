<?php
$page_title = 'Home';
require_once 'includes/header.php';

// Redirect logged-in users to their respective dashboards
if (isLoggedIn()) {
    $role = $_SESSION['role'];
    switch ($role) {
        case 'admin':
            header('Location: /admin/dashboard.php');
            break;
        case 'police':
            header('Location: /police/dashboard.php');
            break;
        case 'citizen':
            header('Location: /citizen/dashboard.php');
            break;
    }
    exit();
}
?>

<div class="hero-section" style="text-align: center; padding: 4rem 0; background: linear-gradient(135deg, var(--bg-secondary) 0%, var(--bg-tertiary) 100%); border-radius: var(--border-radius-lg); margin-bottom: 3rem;">
    <div class="hero-content animate-fade-in">
        <img src="/assets/police-force(3).png" alt="LPF" style="width: 120px; height: 120px; margin-bottom: 2rem; border-radius: 50%; box-shadow: var(--shadow-lg);">
        <h1 style="font-size: 3rem; margin-bottom: 1rem; background: linear-gradient(135deg, var(--primary-color), var(--accent-color)); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text;">
            CRMS
        </h1>
        <h2 style="font-size: 1.5rem; color: var(--text-secondary); margin-bottom: 3rem; font-weight: 400;">
            Lesotho Police Force
        </h2>

        <div class="cta-buttons" style="display: flex; gap: 1rem; justify-content: center; flex-wrap: wrap;">
            <a href="/auth/login.php" class="btn btn-primary btn-lg">
                <i class="fas fa-sign-in-alt"></i>
                Login
            </a>
            <a href="/auth/register.php" class="btn btn-outline btn-lg">
                <i class="fas fa-user-plus"></i>
                Register
            </a>
        </div>
    </div>
</div>

<style>
.text-success {
    color: var(--success-color) !important;
}

.features-grid .card {
    transition: var(--transition);
}

.features-grid .card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-xl);
}

.security-feature {
    transition: var(--transition);
}

.security-feature:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-lg);
}

.hero-content {
    animation-delay: 0.2s;
}

.features-grid .card:nth-child(1) {
    animation-delay: 0.4s;
}

.features-grid .card:nth-child(2) {
    animation-delay: 0.6s;
}

.features-grid .card:nth-child(3) {
    animation-delay: 0.8s;
}

@media (max-width: 768px) {
    .hero-section h1 {
        font-size: 2rem !important;
    }
    
    .hero-section h2 {
        font-size: 1.2rem !important;
    }
    
    .cta-buttons {
        flex-direction: column !important;
        align-items: center;
    }
    
    .cta-buttons .btn {
        width: 100%;
        max-width: 300px;
    }
}
</style>

<?php require_once 'includes/footer.php'; ?>
