<?php
$page_title = 'Add Case';
require_once '../config/database.php';
requireAuth('police');

$error = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $title = sanitizeInput($_POST['title']);
    $description = sanitizeInput($_POST['description']);
    $crime_type = sanitizeInput($_POST['crime_type']);
    $suspect_name = sanitizeInput($_POST['suspect_name']);
    $suspect_id_number = sanitizeInput($_POST['suspect_id_number']);
    $suspect_address = sanitizeInput($_POST['suspect_address']);
    $victim_name = sanitizeInput($_POST['victim_name']);
    $victim_contact = sanitizeInput($_POST['victim_contact']);
    $incident_date = $_POST['incident_date'];
    $incident_location = sanitizeInput($_POST['incident_location']);
    $priority = $_POST['priority'];
    
    if (empty($title) || empty($description) || empty($crime_type) || empty($incident_date) || empty($incident_location)) {
        $error = 'Please fill in all required fields.';
    } else {
        try {
            $database = new Database();
            $conn = $database->getConnection();
            
            // Generate case number
            $case_number = 'CASE-' . date('Y') . '-' . str_pad(rand(1, 9999), 4, '0', STR_PAD_LEFT);
            
            $stmt = $conn->prepare("
                INSERT INTO criminal_records (
                    case_number, title, description, crime_type, suspect_name, suspect_id_number, 
                    suspect_address, victim_name, victim_contact, incident_date, incident_location, 
                    priority, assigned_officer_id, created_by
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ");
            
            if ($stmt->execute([
                $case_number, $title, $description, $crime_type, $suspect_name, $suspect_id_number,
                $suspect_address, $victim_name, $victim_contact, $incident_date, $incident_location,
                $priority, $_SESSION['user_id'], $_SESSION['user_id']
            ])) {
                $_SESSION['flash_message'] = 'Case created successfully. Case Number: ' . $case_number;
                $_SESSION['flash_type'] = 'success';
                header('Location: /police/cases.php');
                exit();
            } else {
                $error = 'Failed to create case. Please try again.';
            }
        } catch (PDOException $e) {
            $error = 'Database error. Please try again later.';
        }
    }
}

require_once '../includes/header.php';
?>

<div class="page-header" style="margin-bottom: 2rem;">
    <h1><i class="fas fa-plus"></i> File New Case</h1>
    <a href="/police/cases.php" class="btn btn-outline">
        <i class="fas fa-arrow-left"></i> Back to Cases
    </a>
</div>

<div class="card" style="max-width: 800px; margin: 0 auto;">
    <div class="card-body">
        <?php if ($error): ?>
            <div class="alert alert-error" style="margin-bottom: 1.5rem;">
                <i class="fas fa-exclamation-circle"></i>
                <?php echo htmlspecialchars($error); ?>
            </div>
        <?php endif; ?>
        
        <form method="POST" data-validate>
            <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
            
            <h3 style="margin-bottom: 1rem; color: var(--primary-color);">Case Information</h3>
            
            <div class="form-group">
                <label class="form-label">Case Title *</label>
                <input type="text" name="title" class="form-control" required 
                       value="<?php echo isset($_POST['title']) ? htmlspecialchars($_POST['title']) : ''; ?>">
            </div>
            
            <div class="form-group">
                <label class="form-label">Description *</label>
                <textarea name="description" class="form-control" rows="4" required><?php echo isset($_POST['description']) ? htmlspecialchars($_POST['description']) : ''; ?></textarea>
            </div>
            
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 1rem;">
                <div class="form-group">
                    <label class="form-label">Crime Type *</label>
                    <select name="crime_type" class="form-control" required>
                        <option value="">Select Crime Type</option>
                        <option value="Theft">Theft</option>
                        <option value="Assault">Assault</option>
                        <option value="Burglary">Burglary</option>
                        <option value="Fraud">Fraud</option>
                        <option value="Vandalism">Vandalism</option>
                        <option value="Drug Offense">Drug Offense</option>
                        <option value="Domestic Violence">Domestic Violence</option>
                        <option value="Traffic Violation">Traffic Violation</option>
                        <option value="Murder">Murder</option>
                        <option value="Robbery">Robbery</option>
                        <option value="Other">Other</option>
                    </select>
                </div>
                <div class="form-group">
                    <label class="form-label">Priority</label>
                    <select name="priority" class="form-control">
                        <option value="low">Low</option>
                        <option value="medium" selected>Medium</option>
                        <option value="high">High</option>
                        <option value="critical">Critical</option>
                    </select>
                </div>
            </div>
            
            <h3 style="margin: 2rem 0 1rem; color: var(--primary-color);">Incident Details</h3>
            
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 1rem;">
                <div class="form-group">
                    <label class="form-label">Incident Date *</label>
                    <input type="date" name="incident_date" class="form-control" required 
                           value="<?php echo isset($_POST['incident_date']) ? $_POST['incident_date'] : ''; ?>">
                </div>
                <div class="form-group">
                    <label class="form-label">Incident Location *</label>
                    <input type="text" name="incident_location" class="form-control" required 
                           value="<?php echo isset($_POST['incident_location']) ? htmlspecialchars($_POST['incident_location']) : ''; ?>">
                </div>
            </div>
            
            <h3 style="margin: 2rem 0 1rem; color: var(--primary-color);">Suspect Information</h3>
            
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 1rem;">
                <div class="form-group">
                    <label class="form-label">Suspect Name</label>
                    <input type="text" name="suspect_name" class="form-control" 
                           value="<?php echo isset($_POST['suspect_name']) ? htmlspecialchars($_POST['suspect_name']) : ''; ?>">
                </div>
                <div class="form-group">
                    <label class="form-label">Suspect ID Number</label>
                    <input type="text" name="suspect_id_number" class="form-control" 
                           value="<?php echo isset($_POST['suspect_id_number']) ? htmlspecialchars($_POST['suspect_id_number']) : ''; ?>">
                </div>
            </div>
            
            <div class="form-group">
                <label class="form-label">Suspect Address</label>
                <textarea name="suspect_address" class="form-control" rows="2"><?php echo isset($_POST['suspect_address']) ? htmlspecialchars($_POST['suspect_address']) : ''; ?></textarea>
            </div>
            
            <h3 style="margin: 2rem 0 1rem; color: var(--primary-color);">Victim Information</h3>
            
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 1rem;">
                <div class="form-group">
                    <label class="form-label">Victim Name</label>
                    <input type="text" name="victim_name" class="form-control" 
                           value="<?php echo isset($_POST['victim_name']) ? htmlspecialchars($_POST['victim_name']) : ''; ?>">
                </div>
                <div class="form-group">
                    <label class="form-label">Victim Contact</label>
                    <input type="text" name="victim_contact" class="form-control" 
                           value="<?php echo isset($_POST['victim_contact']) ? htmlspecialchars($_POST['victim_contact']) : ''; ?>">
                </div>
            </div>
            
            <div style="display: flex; gap: 1rem; justify-content: end; margin-top: 2rem;">
                <a href="/police/cases.php" class="btn btn-outline">Cancel</a>
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-save"></i> Create Case
                </button>
            </div>
        </form>
    </div>
</div>

<style>
.alert {
    padding: 1rem;
    border-radius: var(--border-radius);
    border: 1px solid;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.alert-error {
    background: rgba(239, 68, 68, 0.1);
    border-color: var(--danger-color);
    color: var(--danger-color);
}

.page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
}
</style>

<?php require_once '../includes/footer.php'; ?>
