<?php
$page_title = 'Cases';
require_once '../config/database.php';
requireAuth('police');

$database = new Database();
$conn = $database->getConnection();
$officer_id = $_SESSION['user_id'];

// Handle case actions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action'])) {
        $action = $_POST['action'];
        $case_id = (int)$_POST['case_id'];
        
        switch ($action) {
            case 'update_status':
                $status = $_POST['status'];
                $stmt = $conn->prepare("UPDATE criminal_records SET status = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ? AND assigned_officer_id = ?");
                $stmt->execute([$status, $case_id, $officer_id]);
                $_SESSION['flash_message'] = 'Case status updated.';
                $_SESSION['flash_type'] = 'success';
                break;
        }
        header('Location: /police/cases.php');
        exit();
    }
}

// Get cases with search and filter
$search = $_GET['search'] ?? '';
$status_filter = $_GET['status'] ?? '';
$priority_filter = $_GET['priority'] ?? '';

$where_conditions = ["cr.assigned_officer_id = ?"];
$params = [$officer_id];

if ($search) {
    $where_conditions[] = "(case_number LIKE ? OR title LIKE ? OR suspect_name LIKE ?)";
    $params[] = "%$search%";
    $params[] = "%$search%";
    $params[] = "%$search%";
}

if ($status_filter) {
    $where_conditions[] = "cr.status = ?";
    $params[] = $status_filter;
}

if ($priority_filter) {
    $where_conditions[] = "cr.priority = ?";
    $params[] = $priority_filter;
}

$where_clause = 'WHERE ' . implode(' AND ', $where_conditions);

$stmt = $conn->prepare("
    SELECT cr.*, u.full_name as created_by_name 
    FROM criminal_records cr 
    LEFT JOIN users u ON cr.created_by = u.id 
    $where_clause 
    ORDER BY cr.updated_at DESC
");
$stmt->execute($params);
$cases = $stmt->fetchAll();

require_once '../includes/header.php';
?>

<div class="page-header" style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 2rem;">
    <h1><i class="fas fa-folder-open"></i> My Cases</h1>
    <a href="/police/add_case.php" class="btn btn-primary">
        <i class="fas fa-plus"></i> New Case
    </a>
</div>

<!-- Filters -->
<div class="card" style="margin-bottom: 2rem;">
    <div class="card-body">
        <form method="GET" style="display: grid; grid-template-columns: 1fr 150px 150px auto; gap: 1rem; align-items: end;">
            <div class="form-group" style="margin: 0;">
                <label class="form-label">Search</label>
                <input type="text" name="search" class="form-control" placeholder="Case number, title, or suspect" value="<?php echo htmlspecialchars($search); ?>">
            </div>
            <div class="form-group" style="margin: 0;">
                <label class="form-label">Status</label>
                <select name="status" class="form-control">
                    <option value="">All Status</option>
                    <option value="ongoing" <?php echo $status_filter === 'ongoing' ? 'selected' : ''; ?>>Ongoing</option>
                    <option value="solved" <?php echo $status_filter === 'solved' ? 'selected' : ''; ?>>Solved</option>
                    <option value="paused" <?php echo $status_filter === 'paused' ? 'selected' : ''; ?>>Paused</option>
                    <option value="closed" <?php echo $status_filter === 'closed' ? 'selected' : ''; ?>>Closed</option>
                </select>
            </div>
            <div class="form-group" style="margin: 0;">
                <label class="form-label">Priority</label>
                <select name="priority" class="form-control">
                    <option value="">All Priority</option>
                    <option value="low" <?php echo $priority_filter === 'low' ? 'selected' : ''; ?>>Low</option>
                    <option value="medium" <?php echo $priority_filter === 'medium' ? 'selected' : ''; ?>>Medium</option>
                    <option value="high" <?php echo $priority_filter === 'high' ? 'selected' : ''; ?>>High</option>
                    <option value="critical" <?php echo $priority_filter === 'critical' ? 'selected' : ''; ?>>Critical</option>
                </select>
            </div>
            <button type="submit" class="btn btn-secondary">Filter</button>
        </form>
    </div>
</div>

<!-- Cases Table -->
<div class="card">
    <div class="card-body" style="padding: 0;">
        <div class="table-container">
            <table class="table">
                <thead>
                    <tr>
                        <th>Case</th>
                        <th>Crime Type</th>
                        <th>Status</th>
                        <th>Priority</th>
                        <th>Incident Date</th>
                        <th>Updated</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($cases as $case): ?>
                        <tr>
                            <td>
                                <div>
                                    <strong><?php echo htmlspecialchars($case['case_number']); ?></strong><br>
                                    <small style="color: var(--text-muted);"><?php echo htmlspecialchars(substr($case['title'], 0, 40)) . (strlen($case['title']) > 40 ? '...' : ''); ?></small>
                                </div>
                            </td>
                            <td><?php echo htmlspecialchars($case['crime_type']); ?></td>
                            <td>
                                <span class="badge badge-<?php 
                                    echo $case['status'] === 'solved' ? 'success' : 
                                        ($case['status'] === 'ongoing' ? 'info' : 
                                        ($case['status'] === 'paused' ? 'warning' : 'secondary')); 
                                ?>">
                                    <?php echo ucfirst($case['status']); ?>
                                </span>
                            </td>
                            <td>
                                <span class="badge badge-<?php 
                                    echo $case['priority'] === 'critical' ? 'danger' : 
                                        ($case['priority'] === 'high' ? 'warning' : 
                                        ($case['priority'] === 'medium' ? 'info' : 'secondary')); 
                                ?>">
                                    <?php echo ucfirst($case['priority']); ?>
                                </span>
                            </td>
                            <td><?php echo date('M j, Y', strtotime($case['incident_date'])); ?></td>
                            <td><?php echo date('M j, Y', strtotime($case['updated_at'])); ?></td>
                            <td>
                                <div style="display: flex; gap: 0.5rem;">
                                    <a href="/police/view_case.php?id=<?php echo $case['id']; ?>" class="btn btn-sm btn-outline">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="/police/edit_case.php?id=<?php echo $case['id']; ?>" class="btn btn-sm btn-primary">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    
                                    <!-- Quick Status Update -->
                                    <div class="dropdown" style="position: relative; display: inline-block;">
                                        <button class="btn btn-sm btn-secondary" onclick="toggleDropdown(<?php echo $case['id']; ?>)">
                                            <i class="fas fa-cog"></i>
                                        </button>
                                        <div id="dropdown-<?php echo $case['id']; ?>" class="dropdown-menu" style="display: none; position: absolute; top: 100%; right: 0; background: var(--bg-card); border: 1px solid var(--border-color); border-radius: var(--border-radius); box-shadow: var(--shadow-lg); z-index: 1000; min-width: 120px;">
                                            <form method="POST" style="margin: 0;">
                                                <input type="hidden" name="action" value="update_status">
                                                <input type="hidden" name="case_id" value="<?php echo $case['id']; ?>">
                                                <button type="submit" name="status" value="ongoing" class="dropdown-item">Ongoing</button>
                                                <button type="submit" name="status" value="solved" class="dropdown-item">Solved</button>
                                                <button type="submit" name="status" value="paused" class="dropdown-item">Paused</button>
                                                <button type="submit" name="status" value="closed" class="dropdown-item">Closed</button>
                                            </form>
                                        </div>
                                    </div>
                                </div>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
    </div>
</div>

<?php if (empty($cases)): ?>
    <div style="text-align: center; padding: 3rem; color: var(--text-muted);">
        <i class="fas fa-folder-open" style="font-size: 4rem; margin-bottom: 1rem; opacity: 0.3;"></i>
        <h3>No cases found</h3>
        <p>You don't have any cases assigned or try adjusting your search criteria.</p>
        <a href="/police/add_case.php" class="btn btn-primary" style="margin-top: 1rem;">
            <i class="fas fa-plus"></i> Create New Case
        </a>
    </div>
<?php endif; ?>

<style>
.dropdown-item {
    display: block;
    width: 100%;
    padding: 0.5rem 1rem;
    background: none;
    border: none;
    text-align: left;
    color: var(--text-secondary);
    cursor: pointer;
    transition: var(--transition);
}

.dropdown-item:hover {
    background: var(--bg-hover);
    color: var(--text-primary);
}

.dropdown-menu {
    animation: fadeIn 0.2s ease-out;
}
</style>

<script>
function toggleDropdown(caseId) {
    const dropdown = document.getElementById('dropdown-' + caseId);
    const isVisible = dropdown.style.display !== 'none';
    
    // Close all dropdowns
    document.querySelectorAll('.dropdown-menu').forEach(menu => {
        menu.style.display = 'none';
    });
    
    // Toggle current dropdown
    dropdown.style.display = isVisible ? 'none' : 'block';
}

// Close dropdowns when clicking outside
document.addEventListener('click', function(e) {
    if (!e.target.closest('.dropdown')) {
        document.querySelectorAll('.dropdown-menu').forEach(menu => {
            menu.style.display = 'none';
        });
    }
});
</script>

<?php require_once '../includes/footer.php'; ?>
