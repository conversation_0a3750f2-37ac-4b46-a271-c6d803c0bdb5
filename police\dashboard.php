<?php
$page_title = 'Police Dashboard';
require_once '../config/database.php';
requireAuth('police');

try {
    $database = new Database();
    $conn = $database->getConnection();
    $officer_id = $_SESSION['user_id'];
    
    // Get officer's case statistics
    $stats = [];
    
    // Cases assigned to this officer
    $stmt = $conn->prepare("SELECT COUNT(*) as total FROM criminal_records WHERE assigned_officer_id = ?");
    $stmt->execute([$officer_id]);
    $stats['assigned_cases'] = $stmt->fetch()['total'];
    
    // Cases by status for this officer
    $stmt = $conn->prepare("SELECT status, COUNT(*) as count FROM criminal_records WHERE assigned_officer_id = ? GROUP BY status");
    $stmt->execute([$officer_id]);
    $case_status = [];
    while ($row = $stmt->fetch()) {
        $case_status[$row['status']] = $row['count'];
    }
    
    // Recent cases assigned to this officer
    $stmt = $conn->prepare("
        SELECT cr.*, u.full_name as created_by_name 
        FROM criminal_records cr 
        LEFT JOIN users u ON cr.created_by = u.id 
        WHERE cr.assigned_officer_id = ? 
        ORDER BY cr.updated_at DESC 
        LIMIT 5
    ");
    $stmt->execute([$officer_id]);
    $recent_cases = $stmt->fetchAll();
    
    // Cases created by this officer
    $stmt = $conn->prepare("SELECT COUNT(*) as total FROM criminal_records WHERE created_by = ?");
    $stmt->execute([$officer_id]);
    $stats['created_cases'] = $stmt->fetch()['total'];
    
} catch (PDOException $e) {
    $error = "Database error: " . $e->getMessage();
}

require_once '../includes/header.php';
?>

<div class="dashboard-header" style="margin-bottom: 2rem;">
    <h1 class="animate-fade-in">
        <i class="fas fa-tachometer-alt"></i>
        Dashboard
    </h1>
</div>

<!-- Statistics Cards -->
<div class="stats-grid" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 1.5rem; margin-bottom: 3rem;">
    
    <!-- Assigned Cases -->
    <div class="card animate-fade-in" style="background: linear-gradient(135deg, var(--primary-color), #1d4ed8);">
        <div class="card-body" style="color: white;">
            <div style="display: flex; justify-content: space-between; align-items: center;">
                <div>
                    <h3 style="font-size: 2.5rem; margin: 0; font-weight: 700;">
                        <?php echo number_format($stats['assigned_cases']); ?>
                    </h3>
                    <p style="margin: 0; opacity: 0.9;">Assigned Cases</p>
                </div>
                <i class="fas fa-folder" style="font-size: 3rem; opacity: 0.3;"></i>
            </div>
        </div>
    </div>
    
    <!-- Ongoing Cases -->
    <div class="card animate-fade-in" style="background: linear-gradient(135deg, var(--info-color), #0891b2);">
        <div class="card-body" style="color: white;">
            <div style="display: flex; justify-content: space-between; align-items: center;">
                <div>
                    <h3 style="font-size: 2.5rem; margin: 0; font-weight: 700;">
                        <?php echo number_format($case_status['ongoing'] ?? 0); ?>
                    </h3>
                    <p style="margin: 0; opacity: 0.9;">Ongoing</p>
                </div>
                <i class="fas fa-clock" style="font-size: 3rem; opacity: 0.3;"></i>
            </div>
        </div>
    </div>
    
    <!-- Solved Cases -->
    <div class="card animate-fade-in" style="background: linear-gradient(135deg, var(--success-color), #059669);">
        <div class="card-body" style="color: white;">
            <div style="display: flex; justify-content: space-between; align-items: center;">
                <div>
                    <h3 style="font-size: 2.5rem; margin: 0; font-weight: 700;">
                        <?php echo number_format($case_status['solved'] ?? 0); ?>
                    </h3>
                    <p style="margin: 0; opacity: 0.9;">Solved</p>
                </div>
                <i class="fas fa-check-circle" style="font-size: 3rem; opacity: 0.3;"></i>
            </div>
        </div>
    </div>
    
    <!-- Created Cases -->
    <div class="card animate-fade-in" style="background: linear-gradient(135deg, var(--warning-color), #d97706);">
        <div class="card-body" style="color: white;">
            <div style="display: flex; justify-content: space-between; align-items: center;">
                <div>
                    <h3 style="font-size: 2.5rem; margin: 0; font-weight: 700;">
                        <?php echo number_format($stats['created_cases']); ?>
                    </h3>
                    <p style="margin: 0; opacity: 0.9;">Created Cases</p>
                </div>
                <i class="fas fa-plus-circle" style="font-size: 3rem; opacity: 0.3;"></i>
            </div>
        </div>
    </div>
</div>

<!-- Case Status Chart -->
<div class="card animate-fade-in" style="margin-bottom: 3rem;">
    <div class="card-header">
        <h3 class="card-title">
            <i class="fas fa-chart-pie"></i>
            My Cases by Status
        </h3>
    </div>
    <div class="card-body">
        <canvas id="caseStatusChart" height="300"></canvas>
    </div>
</div>

<!-- Recent Cases -->
<div class="card animate-fade-in">
    <div class="card-header">
        <h3 class="card-title">
            <i class="fas fa-clock"></i>
            Recent Cases
        </h3>
        <a href="/police/cases.php" class="btn btn-sm btn-outline">View All</a>
    </div>
    <div class="card-body" style="padding: 0;">
        <?php if (empty($recent_cases)): ?>
            <div style="padding: 2rem; text-align: center; color: var(--text-muted);">
                <i class="fas fa-inbox" style="font-size: 3rem; margin-bottom: 1rem; opacity: 0.3;"></i>
                <h3>No cases assigned</h3>
                <p>You don't have any cases assigned yet.</p>
            </div>
        <?php else: ?>
            <div class="table-container">
                <table class="table">
                    <thead>
                        <tr>
                            <th>Case Number</th>
                            <th>Title</th>
                            <th>Status</th>
                            <th>Priority</th>
                            <th>Updated</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($recent_cases as $case): ?>
                            <tr>
                                <td>
                                    <strong><?php echo htmlspecialchars($case['case_number']); ?></strong>
                                </td>
                                <td>
                                    <?php echo htmlspecialchars(substr($case['title'], 0, 30)) . (strlen($case['title']) > 30 ? '...' : ''); ?>
                                </td>
                                <td>
                                    <span class="badge badge-<?php 
                                        echo $case['status'] === 'solved' ? 'success' : 
                                            ($case['status'] === 'ongoing' ? 'info' : 
                                            ($case['status'] === 'paused' ? 'warning' : 'secondary')); 
                                    ?>">
                                        <?php echo ucfirst($case['status']); ?>
                                    </span>
                                </td>
                                <td>
                                    <span class="badge badge-<?php 
                                        echo $case['priority'] === 'critical' ? 'danger' : 
                                            ($case['priority'] === 'high' ? 'warning' : 
                                            ($case['priority'] === 'medium' ? 'info' : 'secondary')); 
                                    ?>">
                                        <?php echo ucfirst($case['priority']); ?>
                                    </span>
                                </td>
                                <td><?php echo date('M j, Y', strtotime($case['updated_at'])); ?></td>
                                <td>
                                    <a href="/police/view_case.php?id=<?php echo $case['id']; ?>" class="btn btn-sm btn-outline">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="/police/edit_case.php?id=<?php echo $case['id']; ?>" class="btn btn-sm btn-primary">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        <?php endif; ?>
    </div>
</div>

<!-- Quick Actions -->
<div class="quick-actions" style="margin-top: 3rem;">
    <h3 style="margin-bottom: 1.5rem;">
        <i class="fas fa-bolt"></i>
        Quick Actions
    </h3>
    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem;">
        <a href="/police/add_case.php" class="btn btn-primary">
            <i class="fas fa-plus"></i>
            New Case
        </a>
        <a href="/police/cases.php" class="btn btn-success">
            <i class="fas fa-folder-open"></i>
            My Cases
        </a>
        <a href="/police/reports.php" class="btn btn-warning">
            <i class="fas fa-chart-bar"></i>
            Reports
        </a>
        <a href="/police/search.php" class="btn btn-secondary">
            <i class="fas fa-search"></i>
            Search Cases
        </a>
    </div>
</div>

<script>
// Case Status Chart
const caseStatusCtx = document.getElementById('caseStatusChart').getContext('2d');
new Chart(caseStatusCtx, {
    type: 'doughnut',
    data: {
        labels: ['Ongoing', 'Solved', 'Paused', 'Closed'],
        datasets: [{
            data: [
                <?php echo $case_status['ongoing'] ?? 0; ?>,
                <?php echo $case_status['solved'] ?? 0; ?>,
                <?php echo $case_status['paused'] ?? 0; ?>,
                <?php echo $case_status['closed'] ?? 0; ?>
            ],
            backgroundColor: [
                'rgba(6, 182, 212, 0.8)',
                'rgba(16, 185, 129, 0.8)',
                'rgba(245, 158, 11, 0.8)',
                'rgba(100, 116, 139, 0.8)'
            ],
            borderColor: [
                'rgba(6, 182, 212, 1)',
                'rgba(16, 185, 129, 1)',
                'rgba(245, 158, 11, 1)',
                'rgba(100, 116, 139, 1)'
            ],
            borderWidth: 2
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                position: 'bottom',
                labels: {
                    color: 'rgba(248, 250, 252, 0.8)',
                    padding: 20
                }
            }
        }
    }
});
</script>

<style>
.stats-grid .card {
    transition: var(--transition);
}

.stats-grid .card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-xl);
}

.card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.quick-actions .btn {
    padding: 1rem;
    text-align: center;
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    align-items: center;
}

.quick-actions .btn i {
    font-size: 1.5rem;
}

@media (max-width: 768px) {
    .stats-grid {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)) !important;
    }
}
</style>

<?php require_once '../includes/footer.php'; ?>
