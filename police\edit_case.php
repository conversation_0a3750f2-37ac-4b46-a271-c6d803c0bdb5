<?php
$page_title = 'Edit Case';
require_once '../config/database.php';
requireAuth('police');

$case_id = (int)($_GET['id'] ?? 0);
$officer_id = $_SESSION['user_id'];
$error = '';

if (!$case_id) {
    header('Location: /police/cases.php');
    exit();
}

$database = new Database();
$conn = $database->getConnection();

// Get case data - only cases assigned to or created by this officer
try {
    $stmt = $conn->prepare("SELECT * FROM criminal_records WHERE id = ? AND (assigned_officer_id = ? OR created_by = ?)");
    $stmt->execute([$case_id, $officer_id, $officer_id]);
    $case = $stmt->fetch();
    
    if (!$case) {
        $_SESSION['flash_message'] = 'Case not found or access denied.';
        $_SESSION['flash_type'] = 'error';
        header('Location: /police/cases.php');
        exit();
    }
} catch (PDOException $e) {
    $error = 'Database error. Please try again later.';
}

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $title = sanitizeInput($_POST['title']);
    $description = sanitizeInput($_POST['description']);
    $crime_type = sanitizeInput($_POST['crime_type']);
    $suspect_name = sanitizeInput($_POST['suspect_name']);
    $suspect_id_number = sanitizeInput($_POST['suspect_id_number']);
    $suspect_address = sanitizeInput($_POST['suspect_address']);
    $victim_name = sanitizeInput($_POST['victim_name']);
    $victim_contact = sanitizeInput($_POST['victim_contact']);
    $incident_date = $_POST['incident_date'];
    $incident_location = sanitizeInput($_POST['incident_location']);
    $status = $_POST['status'];
    $priority = $_POST['priority'];
    
    if (empty($title) || empty($description) || empty($crime_type) || empty($incident_date) || empty($incident_location)) {
        $error = 'Please fill in all required fields.';
    } else {
        try {
            $stmt = $conn->prepare("
                UPDATE criminal_records SET 
                    title = ?, description = ?, crime_type = ?, suspect_name = ?, suspect_id_number = ?, 
                    suspect_address = ?, victim_name = ?, victim_contact = ?, incident_date = ?, 
                    incident_location = ?, status = ?, priority = ?, updated_at = CURRENT_TIMESTAMP
                WHERE id = ? AND (assigned_officer_id = ? OR created_by = ?)
            ");
            
            if ($stmt->execute([
                $title, $description, $crime_type, $suspect_name, $suspect_id_number,
                $suspect_address, $victim_name, $victim_contact, $incident_date,
                $incident_location, $status, $priority, $case_id, $officer_id, $officer_id
            ])) {
                $_SESSION['flash_message'] = 'Case updated successfully.';
                $_SESSION['flash_type'] = 'success';
                header('Location: /police/view_case.php?id=' . $case_id);
                exit();
            } else {
                $error = 'Failed to update case. Please try again.';
            }
        } catch (PDOException $e) {
            $error = 'Database error. Please try again later.';
        }
    }
}

require_once '../includes/header.php';
?>

<div class="page-header" style="margin-bottom: 2rem;">
    <h1><i class="fas fa-edit"></i> Edit Case</h1>
    <div style="display: flex; gap: 1rem;">
        <a href="/police/view_case.php?id=<?php echo $case['id']; ?>" class="btn btn-outline">
            <i class="fas fa-eye"></i> View Case
        </a>
        <a href="/police/cases.php" class="btn btn-outline">
            <i class="fas fa-arrow-left"></i> Back to Cases
        </a>
    </div>
</div>

<div class="card" style="max-width: 800px; margin: 0 auto;">
    <div class="card-body">
        <?php if ($error): ?>
            <div class="alert alert-error" style="margin-bottom: 1.5rem;">
                <i class="fas fa-exclamation-circle"></i>
                <?php echo htmlspecialchars($error); ?>
            </div>
        <?php endif; ?>
        
        <form method="POST" data-validate>
            <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
            
            <h3 style="margin-bottom: 1rem; color: var(--primary-color);">Case Information</h3>
            
            <div class="form-group">
                <label class="form-label">Case Number</label>
                <input type="text" class="form-control" value="<?php echo htmlspecialchars($case['case_number']); ?>" readonly style="background: var(--bg-tertiary); opacity: 0.7;">
            </div>
            
            <div class="form-group">
                <label class="form-label">Case Title *</label>
                <input type="text" name="title" class="form-control" required value="<?php echo htmlspecialchars($case['title']); ?>">
            </div>
            
            <div class="form-group">
                <label class="form-label">Description *</label>
                <textarea name="description" class="form-control" rows="4" required><?php echo htmlspecialchars($case['description']); ?></textarea>
            </div>
            
            <div style="display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 1rem;">
                <div class="form-group">
                    <label class="form-label">Crime Type *</label>
                    <select name="crime_type" class="form-control" required>
                        <option value="Theft" <?php echo $case['crime_type'] === 'Theft' ? 'selected' : ''; ?>>Theft</option>
                        <option value="Assault" <?php echo $case['crime_type'] === 'Assault' ? 'selected' : ''; ?>>Assault</option>
                        <option value="Burglary" <?php echo $case['crime_type'] === 'Burglary' ? 'selected' : ''; ?>>Burglary</option>
                        <option value="Fraud" <?php echo $case['crime_type'] === 'Fraud' ? 'selected' : ''; ?>>Fraud</option>
                        <option value="Vandalism" <?php echo $case['crime_type'] === 'Vandalism' ? 'selected' : ''; ?>>Vandalism</option>
                        <option value="Drug Offense" <?php echo $case['crime_type'] === 'Drug Offense' ? 'selected' : ''; ?>>Drug Offense</option>
                        <option value="Domestic Violence" <?php echo $case['crime_type'] === 'Domestic Violence' ? 'selected' : ''; ?>>Domestic Violence</option>
                        <option value="Traffic Violation" <?php echo $case['crime_type'] === 'Traffic Violation' ? 'selected' : ''; ?>>Traffic Violation</option>
                        <option value="Murder" <?php echo $case['crime_type'] === 'Murder' ? 'selected' : ''; ?>>Murder</option>
                        <option value="Robbery" <?php echo $case['crime_type'] === 'Robbery' ? 'selected' : ''; ?>>Robbery</option>
                        <option value="Other" <?php echo $case['crime_type'] === 'Other' ? 'selected' : ''; ?>>Other</option>
                    </select>
                </div>
                <div class="form-group">
                    <label class="form-label">Status</label>
                    <select name="status" class="form-control">
                        <option value="ongoing" <?php echo $case['status'] === 'ongoing' ? 'selected' : ''; ?>>Ongoing</option>
                        <option value="solved" <?php echo $case['status'] === 'solved' ? 'selected' : ''; ?>>Solved</option>
                        <option value="paused" <?php echo $case['status'] === 'paused' ? 'selected' : ''; ?>>Paused</option>
                        <option value="closed" <?php echo $case['status'] === 'closed' ? 'selected' : ''; ?>>Closed</option>
                    </select>
                </div>
                <div class="form-group">
                    <label class="form-label">Priority</label>
                    <select name="priority" class="form-control">
                        <option value="low" <?php echo $case['priority'] === 'low' ? 'selected' : ''; ?>>Low</option>
                        <option value="medium" <?php echo $case['priority'] === 'medium' ? 'selected' : ''; ?>>Medium</option>
                        <option value="high" <?php echo $case['priority'] === 'high' ? 'selected' : ''; ?>>High</option>
                        <option value="critical" <?php echo $case['priority'] === 'critical' ? 'selected' : ''; ?>>Critical</option>
                    </select>
                </div>
            </div>
            
            <h3 style="margin: 2rem 0 1rem; color: var(--primary-color);">Incident Details</h3>
            
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 1rem;">
                <div class="form-group">
                    <label class="form-label">Incident Date *</label>
                    <input type="date" name="incident_date" class="form-control" required value="<?php echo $case['incident_date']; ?>">
                </div>
                <div class="form-group">
                    <label class="form-label">Incident Location *</label>
                    <input type="text" name="incident_location" class="form-control" required value="<?php echo htmlspecialchars($case['incident_location']); ?>">
                </div>
            </div>
            
            <h3 style="margin: 2rem 0 1rem; color: var(--primary-color);">Suspect Information</h3>
            
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 1rem;">
                <div class="form-group">
                    <label class="form-label">Suspect Name</label>
                    <input type="text" name="suspect_name" class="form-control" value="<?php echo htmlspecialchars($case['suspect_name'] ?? ''); ?>">
                </div>
                <div class="form-group">
                    <label class="form-label">Suspect ID Number</label>
                    <input type="text" name="suspect_id_number" class="form-control" value="<?php echo htmlspecialchars($case['suspect_id_number'] ?? ''); ?>">
                </div>
            </div>
            
            <div class="form-group">
                <label class="form-label">Suspect Address</label>
                <textarea name="suspect_address" class="form-control" rows="2"><?php echo htmlspecialchars($case['suspect_address'] ?? ''); ?></textarea>
            </div>
            
            <h3 style="margin: 2rem 0 1rem; color: var(--primary-color);">Victim Information</h3>
            
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 1rem;">
                <div class="form-group">
                    <label class="form-label">Victim Name</label>
                    <input type="text" name="victim_name" class="form-control" value="<?php echo htmlspecialchars($case['victim_name'] ?? ''); ?>">
                </div>
                <div class="form-group">
                    <label class="form-label">Victim Contact</label>
                    <input type="text" name="victim_contact" class="form-control" value="<?php echo htmlspecialchars($case['victim_contact'] ?? ''); ?>">
                </div>
            </div>
            
            <div style="display: flex; gap: 1rem; justify-content: end; margin-top: 2rem;">
                <a href="/police/view_case.php?id=<?php echo $case['id']; ?>" class="btn btn-outline">Cancel</a>
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-save"></i> Update Case
                </button>
            </div>
        </form>
    </div>
</div>

<style>
.alert {
    padding: 1rem;
    border-radius: var(--border-radius);
    border: 1px solid;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.alert-error {
    background: rgba(239, 68, 68, 0.1);
    border-color: var(--danger-color);
    color: var(--danger-color);
}

.page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
}
</style>

<?php require_once '../includes/footer.php'; ?>
