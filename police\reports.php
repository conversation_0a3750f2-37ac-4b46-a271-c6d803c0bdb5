<?php
$page_title = 'Reports';
require_once '../config/database.php';
requireAuth('police');

$database = new Database();
$conn = $database->getConnection();
$officer_id = $_SESSION['user_id'];

// Handle report generation
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['generate_report'])) {
    $report_type = $_POST['report_type'];
    $date_from = $_POST['date_from'];
    $date_to = $_POST['date_to'];
    
    header('Content-Type: text/csv');
    header('Content-Disposition: attachment; filename="' . $report_type . '_report_' . date('Y-m-d') . '.csv"');
    
    $output = fopen('php://output', 'w');
    
    switch ($report_type) {
        case 'my_cases':
            fputcsv($output, ['Case Number', 'Title', 'Crime Type', 'Status', 'Priority', 'Incident Date', 'Created Date']);
            $stmt = $conn->prepare("
                SELECT case_number, title, crime_type, status, priority, incident_date, created_at 
                FROM criminal_records 
                WHERE assigned_officer_id = ? AND created_at BETWEEN ? AND ?
            ");
            $stmt->execute([$officer_id, $date_from, $date_to]);
            while ($row = $stmt->fetch()) {
                fputcsv($output, $row);
            }
            break;
            
        case 'created_cases':
            fputcsv($output, ['Case Number', 'Title', 'Crime Type', 'Status', 'Priority', 'Incident Date', 'Created Date']);
            $stmt = $conn->prepare("
                SELECT case_number, title, crime_type, status, priority, incident_date, created_at 
                FROM criminal_records 
                WHERE created_by = ? AND created_at BETWEEN ? AND ?
            ");
            $stmt->execute([$officer_id, $date_from, $date_to]);
            while ($row = $stmt->fetch()) {
                fputcsv($output, $row);
            }
            break;
    }
    
    fclose($output);
    exit();
}

// Get statistics for reports
$stats = [];

// Cases assigned to this officer by status
$stmt = $conn->prepare("SELECT status, COUNT(*) as count FROM criminal_records WHERE assigned_officer_id = ? GROUP BY status");
$stmt->execute([$officer_id]);
$assigned_case_stats = [];
while ($row = $stmt->fetch()) {
    $assigned_case_stats[$row['status']] = $row['count'];
}

// Cases created by this officer by status
$stmt = $conn->prepare("SELECT status, COUNT(*) as count FROM criminal_records WHERE created_by = ? GROUP BY status");
$stmt->execute([$officer_id]);
$created_case_stats = [];
while ($row = $stmt->fetch()) {
    $created_case_stats[$row['status']] = $row['count'];
}

// Monthly case trends for this officer
$stmt = $conn->prepare("
    SELECT DATE_FORMAT(created_at, '%Y-%m') as month, COUNT(*) as count 
    FROM criminal_records 
    WHERE assigned_officer_id = ? AND created_at >= DATE_SUB(NOW(), INTERVAL 12 MONTH) 
    GROUP BY month 
    ORDER BY month
");
$stmt->execute([$officer_id]);
$monthly_assigned = $stmt->fetchAll();

require_once '../includes/header.php';
?>

<div class="page-header" style="margin-bottom: 2rem;">
    <h1><i class="fas fa-chart-bar"></i> Reports</h1>
</div>

<!-- Report Generation -->
<div class="card" style="margin-bottom: 2rem;">
    <div class="card-header">
        <h3 class="card-title"><i class="fas fa-download"></i> Generate Reports</h3>
    </div>
    <div class="card-body">
        <form method="POST">
            <div style="display: grid; grid-template-columns: 1fr 1fr 1fr auto; gap: 1rem; align-items: end;">
                <div class="form-group" style="margin: 0;">
                    <label class="form-label">Report Type</label>
                    <select name="report_type" class="form-control" required>
                        <option value="">Select Report</option>
                        <option value="my_cases">My Assigned Cases</option>
                        <option value="created_cases">Cases I Created</option>
                    </select>
                </div>
                <div class="form-group" style="margin: 0;">
                    <label class="form-label">From Date</label>
                    <input type="date" name="date_from" class="form-control" value="<?php echo date('Y-m-01'); ?>" required>
                </div>
                <div class="form-group" style="margin: 0;">
                    <label class="form-label">To Date</label>
                    <input type="date" name="date_to" class="form-control" value="<?php echo date('Y-m-d'); ?>" required>
                </div>
                <button type="submit" name="generate_report" class="btn btn-primary">
                    <i class="fas fa-download"></i> Download CSV
                </button>
            </div>
        </form>
    </div>
</div>

<!-- Statistics Overview -->
<div style="display: grid; grid-template-columns: 1fr 1fr; gap: 2rem; margin-bottom: 3rem;">
    
    <!-- Assigned Cases Statistics -->
    <div class="card">
        <div class="card-header">
            <h3 class="card-title"><i class="fas fa-folder"></i> Assigned Cases</h3>
        </div>
        <div class="card-body">
            <canvas id="assignedCasesChart" height="200"></canvas>
        </div>
    </div>
    
    <!-- Created Cases Statistics -->
    <div class="card">
        <div class="card-header">
            <h3 class="card-title"><i class="fas fa-plus-circle"></i> Created Cases</h3>
        </div>
        <div class="card-body">
            <canvas id="createdCasesChart" height="200"></canvas>
        </div>
    </div>
</div>

<!-- Monthly Trends -->
<div class="card" style="margin-bottom: 2rem;">
    <div class="card-header">
        <h3 class="card-title"><i class="fas fa-chart-line"></i> Monthly Assigned Cases Trend</h3>
    </div>
    <div class="card-body">
        <canvas id="monthlyTrendsChart" height="300"></canvas>
    </div>
</div>

<!-- Summary Tables -->
<div style="display: grid; grid-template-columns: 1fr 1fr; gap: 2rem;">
    
    <!-- Assigned Cases Summary -->
    <div class="card">
        <div class="card-header">
            <h3 class="card-title">Assigned Cases Summary</h3>
        </div>
        <div class="card-body" style="padding: 0;">
            <table class="table">
                <thead>
                    <tr>
                        <th>Status</th>
                        <th>Count</th>
                        <th>Percentage</th>
                    </tr>
                </thead>
                <tbody>
                    <?php 
                    $total_assigned = array_sum($assigned_case_stats);
                    foreach ($assigned_case_stats as $status => $count): 
                        $percentage = $total_assigned > 0 ? round(($count / $total_assigned) * 100, 1) : 0;
                    ?>
                        <tr>
                            <td>
                                <span class="badge badge-<?php 
                                    echo $status === 'solved' ? 'success' : 
                                        ($status === 'ongoing' ? 'info' : 
                                        ($status === 'paused' ? 'warning' : 'secondary')); 
                                ?>">
                                    <?php echo ucfirst($status); ?>
                                </span>
                            </td>
                            <td><?php echo number_format($count); ?></td>
                            <td><?php echo $percentage; ?>%</td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
    </div>
    
    <!-- Created Cases Summary -->
    <div class="card">
        <div class="card-header">
            <h3 class="card-title">Created Cases Summary</h3>
        </div>
        <div class="card-body" style="padding: 0;">
            <table class="table">
                <thead>
                    <tr>
                        <th>Status</th>
                        <th>Count</th>
                        <th>Percentage</th>
                    </tr>
                </thead>
                <tbody>
                    <?php 
                    $total_created = array_sum($created_case_stats);
                    foreach ($created_case_stats as $status => $count): 
                        $percentage = $total_created > 0 ? round(($count / $total_created) * 100, 1) : 0;
                    ?>
                        <tr>
                            <td>
                                <span class="badge badge-<?php 
                                    echo $status === 'solved' ? 'success' : 
                                        ($status === 'ongoing' ? 'info' : 
                                        ($status === 'paused' ? 'warning' : 'secondary')); 
                                ?>">
                                    <?php echo ucfirst($status); ?>
                                </span>
                            </td>
                            <td><?php echo number_format($count); ?></td>
                            <td><?php echo $percentage; ?>%</td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
    </div>
</div>

<script>
// Assigned Cases Chart
const assignedCasesCtx = document.getElementById('assignedCasesChart').getContext('2d');
new Chart(assignedCasesCtx, {
    type: 'doughnut',
    data: {
        labels: <?php echo json_encode(array_keys($assigned_case_stats)); ?>,
        datasets: [{
            data: <?php echo json_encode(array_values($assigned_case_stats)); ?>,
            backgroundColor: [
                'rgba(6, 182, 212, 0.8)',
                'rgba(16, 185, 129, 0.8)',
                'rgba(245, 158, 11, 0.8)',
                'rgba(100, 116, 139, 0.8)'
            ]
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                position: 'bottom',
                labels: { color: 'rgba(248, 250, 252, 0.8)' }
            }
        }
    }
});

// Created Cases Chart
const createdCasesCtx = document.getElementById('createdCasesChart').getContext('2d');
new Chart(createdCasesCtx, {
    type: 'pie',
    data: {
        labels: <?php echo json_encode(array_keys($created_case_stats)); ?>,
        datasets: [{
            data: <?php echo json_encode(array_values($created_case_stats)); ?>,
            backgroundColor: [
                'rgba(37, 99, 235, 0.8)',
                'rgba(16, 185, 129, 0.8)',
                'rgba(245, 158, 11, 0.8)',
                'rgba(100, 116, 139, 0.8)'
            ]
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                position: 'bottom',
                labels: { color: 'rgba(248, 250, 252, 0.8)' }
            }
        }
    }
});

// Monthly Trends Chart
const monthlyTrendsCtx = document.getElementById('monthlyTrendsChart').getContext('2d');
new Chart(monthlyTrendsCtx, {
    type: 'line',
    data: {
        labels: <?php echo json_encode(array_column($monthly_assigned, 'month')); ?>,
        datasets: [{
            label: 'Assigned Cases',
            data: <?php echo json_encode(array_column($monthly_assigned, 'count')); ?>,
            borderColor: 'rgba(37, 99, 235, 1)',
            backgroundColor: 'rgba(37, 99, 235, 0.1)',
            tension: 0.4,
            fill: true
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                labels: { color: 'rgba(248, 250, 252, 0.8)' }
            }
        },
        scales: {
            y: {
                beginAtZero: true,
                ticks: { color: 'rgba(248, 250, 252, 0.8)' },
                grid: { color: 'rgba(100, 116, 139, 0.3)' }
            },
            x: {
                ticks: { color: 'rgba(248, 250, 252, 0.8)' },
                grid: { color: 'rgba(100, 116, 139, 0.3)' }
            }
        }
    }
});
</script>

<?php require_once '../includes/footer.php'; ?>
