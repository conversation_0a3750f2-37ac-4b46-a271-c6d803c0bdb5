<?php
$page_title = 'Search Cases';
require_once '../config/database.php';
requireAuth('police');

$database = new Database();
$conn = $database->getConnection();

$search_results = [];
$search_performed = false;

if ($_SERVER['REQUEST_METHOD'] === 'GET' && !empty($_GET['q'])) {
    $search_term = sanitizeInput($_GET['q']);
    $search_performed = true;
    
    try {
        $stmt = $conn->prepare("
            SELECT cr.*, 
                   u1.full_name as created_by_name,
                   u2.full_name as officer_name
            FROM criminal_records cr 
            LEFT JOIN users u1 ON cr.created_by = u1.id 
            LEFT JOIN users u2 ON cr.assigned_officer_id = u2.id 
            WHERE cr.case_number LIKE ? 
               OR cr.title LIKE ? 
               OR cr.description LIKE ? 
               OR cr.suspect_name LIKE ? 
               OR cr.victim_name LIKE ?
            ORDER BY cr.created_at DESC
            LIMIT 50
        ");
        
        $search_param = "%$search_term%";
        $stmt->execute([$search_param, $search_param, $search_param, $search_param, $search_param]);
        $search_results = $stmt->fetchAll();
        
    } catch (PDOException $e) {
        $error = 'Search failed. Please try again.';
    }
}

require_once '../includes/header.php';
?>

<div class="page-header" style="margin-bottom: 2rem;">
    <h1><i class="fas fa-search"></i> Search Cases</h1>
    <a href="/police/dashboard.php" class="btn btn-outline">
        <i class="fas fa-arrow-left"></i> Back to Dashboard
    </a>
</div>

<!-- Search Form -->
<div class="card" style="margin-bottom: 2rem;">
    <div class="card-body">
        <form method="GET">
            <div style="display: flex; gap: 1rem; align-items: end;">
                <div class="form-group" style="flex: 1; margin: 0;">
                    <label class="form-label">Search Cases</label>
                    <input type="text" name="q" class="form-control" 
                           placeholder="Enter case number, title, suspect name, or keywords..."
                           value="<?php echo isset($_GET['q']) ? htmlspecialchars($_GET['q']) : ''; ?>"
                           autofocus>
                </div>
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-search"></i> Search
                </button>
            </div>
        </form>
    </div>
</div>

<!-- Search Results -->
<?php if ($search_performed): ?>
    <div class="card">
        <div class="card-header">
            <h3 class="card-title">
                Search Results 
                <?php if (!empty($_GET['q'])): ?>
                    for "<?php echo htmlspecialchars($_GET['q']); ?>"
                <?php endif; ?>
            </h3>
            <span class="badge badge-info"><?php echo count($search_results); ?> results</span>
        </div>
        
        <?php if (empty($search_results)): ?>
            <div class="card-body" style="text-align: center; padding: 3rem;">
                <i class="fas fa-search" style="font-size: 4rem; color: var(--text-muted); opacity: 0.3; margin-bottom: 1rem;"></i>
                <h3>No cases found</h3>
                <p style="color: var(--text-muted);">Try different keywords or check your spelling.</p>
            </div>
        <?php else: ?>
            <div class="card-body" style="padding: 0;">
                <div class="table-container">
                    <table class="table">
                        <thead>
                            <tr>
                                <th>Case</th>
                                <th>Crime Type</th>
                                <th>Status</th>
                                <th>Priority</th>
                                <th>Officer</th>
                                <th>Date</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($search_results as $case): ?>
                                <tr>
                                    <td>
                                        <div>
                                            <strong><?php echo htmlspecialchars($case['case_number']); ?></strong><br>
                                            <small style="color: var(--text-muted);">
                                                <?php echo htmlspecialchars(substr($case['title'], 0, 40)) . (strlen($case['title']) > 40 ? '...' : ''); ?>
                                            </small>
                                        </div>
                                    </td>
                                    <td><?php echo htmlspecialchars($case['crime_type']); ?></td>
                                    <td>
                                        <span class="badge badge-<?php 
                                            echo $case['status'] === 'solved' ? 'success' : 
                                                ($case['status'] === 'ongoing' ? 'info' : 
                                                ($case['status'] === 'paused' ? 'warning' : 'secondary')); 
                                        ?>">
                                            <?php echo ucfirst($case['status']); ?>
                                        </span>
                                    </td>
                                    <td>
                                        <span class="badge badge-<?php 
                                            echo $case['priority'] === 'critical' ? 'danger' : 
                                                ($case['priority'] === 'high' ? 'warning' : 
                                                ($case['priority'] === 'medium' ? 'info' : 'secondary')); 
                                        ?>">
                                            <?php echo ucfirst($case['priority']); ?>
                                        </span>
                                    </td>
                                    <td>
                                        <?php echo $case['officer_name'] ? htmlspecialchars($case['officer_name']) : '<em>Unassigned</em>'; ?>
                                    </td>
                                    <td><?php echo date('M j, Y', strtotime($case['created_at'])); ?></td>
                                    <td>
                                        <div style="display: flex; gap: 0.5rem;">
                                            <a href="/police/view_case.php?id=<?php echo $case['id']; ?>" class="btn btn-sm btn-outline">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <?php if ($case['assigned_officer_id'] == $_SESSION['user_id'] || $case['created_by'] == $_SESSION['user_id']): ?>
                                                <a href="/police/edit_case.php?id=<?php echo $case['id']; ?>" class="btn btn-sm btn-primary">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                            <?php endif; ?>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        <?php endif; ?>
    </div>
<?php else: ?>
    <!-- Search Tips -->
    <div class="card">
        <div class="card-header">
            <h3 class="card-title">Search Tips</h3>
        </div>
        <div class="card-body">
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 2rem;">
                <div>
                    <h4>What you can search for:</h4>
                    <ul>
                        <li>Case numbers (e.g., CASE-2024-1234)</li>
                        <li>Case titles or descriptions</li>
                        <li>Suspect names</li>
                        <li>Victim names</li>
                        <li>Crime types</li>
                    </ul>
                </div>
                <div>
                    <h4>Search tips:</h4>
                    <ul>
                        <li>Use partial words for broader results</li>
                        <li>Search is case-insensitive</li>
                        <li>Results are limited to 50 cases</li>
                        <li>Most recent cases appear first</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
<?php endif; ?>

<style>
.card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

h4 {
    margin: 0 0 0.5rem 0;
    font-size: 1rem;
    font-weight: 600;
    color: var(--text-primary);
}

ul {
    margin: 0.5rem 0 0 1rem;
}

li {
    margin-bottom: 0.25rem;
}
</style>

<?php require_once '../includes/footer.php'; ?>
