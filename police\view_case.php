<?php
$page_title = 'View Case';
require_once '../config/database.php';
requireAuth('police');

$case_id = (int)($_GET['id'] ?? 0);
$officer_id = $_SESSION['user_id'];

if (!$case_id) {
    header('Location: /police/cases.php');
    exit();
}

$database = new Database();
$conn = $database->getConnection();

try {
    $stmt = $conn->prepare("
        SELECT cr.*, 
               u1.full_name as created_by_name,
               u2.full_name as officer_name,
               u2.badge_number as officer_badge
        FROM criminal_records cr 
        LEFT JOIN users u1 ON cr.created_by = u1.id 
        LEFT JOIN users u2 ON cr.assigned_officer_id = u2.id 
        WHERE cr.id = ? AND (cr.assigned_officer_id = ? OR cr.created_by = ?)
    ");
    $stmt->execute([$case_id, $officer_id, $officer_id]);
    $case = $stmt->fetch();
    
    if (!$case) {
        $_SESSION['flash_message'] = 'Case not found or access denied.';
        $_SESSION['flash_type'] = 'error';
        header('Location: /police/cases.php');
        exit();
    }
    
    // Get case updates
    $stmt = $conn->prepare("
        SELECT cu.*, u.full_name as updated_by_name 
        FROM case_updates cu 
        LEFT JOIN users u ON cu.updated_by = u.id 
        WHERE cu.case_id = ? 
        ORDER BY cu.created_at DESC
    ");
    $stmt->execute([$case_id]);
    $updates = $stmt->fetchAll();
    
} catch (PDOException $e) {
    $error = 'Database error. Please try again later.';
}

require_once '../includes/header.php';
?>

<div class="page-header" style="margin-bottom: 2rem;">
    <h1><i class="fas fa-eye"></i> Case Details</h1>
    <div style="display: flex; gap: 1rem;">
        <a href="/police/edit_case.php?id=<?php echo $case['id']; ?>" class="btn btn-primary">
            <i class="fas fa-edit"></i> Edit Case
        </a>
        <a href="/police/cases.php" class="btn btn-outline">
            <i class="fas fa-arrow-left"></i> Back to Cases
        </a>
    </div>
</div>

<div style="display: grid; grid-template-columns: 2fr 1fr; gap: 2rem;">
    <!-- Case Information -->
    <div>
        <div class="card" style="margin-bottom: 2rem;">
            <div class="card-header">
                <h3 class="card-title">Case Information</h3>
                <span class="badge badge-<?php 
                    echo $case['status'] === 'solved' ? 'success' : 
                        ($case['status'] === 'ongoing' ? 'info' : 
                        ($case['status'] === 'paused' ? 'warning' : 'secondary')); 
                ?>">
                    <?php echo ucfirst($case['status']); ?>
                </span>
            </div>
            <div class="card-body">
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 2rem;">
                    <div>
                        <h4>Case Number</h4>
                        <p style="font-size: 1.2rem; font-weight: 600; color: var(--primary-color);">
                            <?php echo htmlspecialchars($case['case_number']); ?>
                        </p>
                        
                        <h4>Title</h4>
                        <p><?php echo htmlspecialchars($case['title']); ?></p>
                        
                        <h4>Crime Type</h4>
                        <p><?php echo htmlspecialchars($case['crime_type']); ?></p>
                        
                        <h4>Priority</h4>
                        <span class="badge badge-<?php 
                            echo $case['priority'] === 'critical' ? 'danger' : 
                                ($case['priority'] === 'high' ? 'warning' : 
                                ($case['priority'] === 'medium' ? 'info' : 'secondary')); 
                        ?>">
                            <?php echo ucfirst($case['priority']); ?>
                        </span>
                    </div>
                    <div>
                        <h4>Incident Date</h4>
                        <p><?php echo date('F j, Y', strtotime($case['incident_date'])); ?></p>
                        
                        <h4>Incident Location</h4>
                        <p><?php echo htmlspecialchars($case['incident_location']); ?></p>
                        
                        <h4>Created By</h4>
                        <p><?php echo htmlspecialchars($case['created_by_name']); ?></p>
                        
                        <h4>Assigned Officer</h4>
                        <p>
                            <?php if ($case['officer_name']): ?>
                                <?php echo htmlspecialchars($case['officer_name']); ?>
                                <?php if ($case['officer_badge']): ?>
                                    <small>(<?php echo htmlspecialchars($case['officer_badge']); ?>)</small>
                                <?php endif; ?>
                            <?php else: ?>
                                <em>Unassigned</em>
                            <?php endif; ?>
                        </p>
                    </div>
                </div>
                
                <h4 style="margin-top: 2rem;">Description</h4>
                <p style="background: var(--bg-tertiary); padding: 1rem; border-radius: var(--border-radius); line-height: 1.6;">
                    <?php echo nl2br(htmlspecialchars($case['description'])); ?>
                </p>
            </div>
        </div>
        
        <!-- Suspect Information -->
        <?php if ($case['suspect_name']): ?>
        <div class="card" style="margin-bottom: 2rem;">
            <div class="card-header">
                <h3 class="card-title">Suspect Information</h3>
            </div>
            <div class="card-body">
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 2rem;">
                    <div>
                        <h4>Name</h4>
                        <p><?php echo htmlspecialchars($case['suspect_name']); ?></p>
                        
                        <?php if ($case['suspect_id_number']): ?>
                        <h4>ID Number</h4>
                        <p><?php echo htmlspecialchars($case['suspect_id_number']); ?></p>
                        <?php endif; ?>
                    </div>
                    <div>
                        <?php if ($case['suspect_address']): ?>
                        <h4>Address</h4>
                        <p><?php echo nl2br(htmlspecialchars($case['suspect_address'])); ?></p>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
        <?php endif; ?>
        
        <!-- Victim Information -->
        <?php if ($case['victim_name']): ?>
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">Victim Information</h3>
            </div>
            <div class="card-body">
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 2rem;">
                    <div>
                        <h4>Name</h4>
                        <p><?php echo htmlspecialchars($case['victim_name']); ?></p>
                    </div>
                    <div>
                        <?php if ($case['victim_contact']): ?>
                        <h4>Contact</h4>
                        <p><?php echo htmlspecialchars($case['victim_contact']); ?></p>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
        <?php endif; ?>
    </div>
    
    <!-- Case Timeline -->
    <div>
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">Case Timeline</h3>
            </div>
            <div class="card-body">
                <div class="timeline">
                    <!-- Case Created -->
                    <div class="timeline-item">
                        <div class="timeline-marker" style="background: var(--primary-color);"></div>
                        <div class="timeline-content">
                            <h5>Case Created</h5>
                            <p style="color: var(--text-muted); font-size: 0.875rem;">
                                <?php echo date('M j, Y g:i A', strtotime($case['created_at'])); ?>
                            </p>
                            <p>by <?php echo htmlspecialchars($case['created_by_name']); ?></p>
                        </div>
                    </div>
                    
                    <!-- Case Updates -->
                    <?php foreach ($updates as $update): ?>
                    <div class="timeline-item">
                        <div class="timeline-marker" style="background: var(--info-color);"></div>
                        <div class="timeline-content">
                            <h5><?php echo ucfirst(str_replace('_', ' ', $update['update_type'])); ?></h5>
                            <p style="color: var(--text-muted); font-size: 0.875rem;">
                                <?php echo date('M j, Y g:i A', strtotime($update['created_at'])); ?>
                            </p>
                            <p><?php echo nl2br(htmlspecialchars($update['update_text'])); ?></p>
                            <small style="color: var(--text-muted);">
                                by <?php echo htmlspecialchars($update['updated_by_name']); ?>
                            </small>
                        </div>
                    </div>
                    <?php endforeach; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.timeline {
    position: relative;
    padding-left: 2rem;
}

.timeline::before {
    content: '';
    position: absolute;
    left: 0.5rem;
    top: 0;
    bottom: 0;
    width: 2px;
    background: var(--border-color);
}

.timeline-item {
    position: relative;
    margin-bottom: 2rem;
}

.timeline-marker {
    position: absolute;
    left: -2rem;
    top: 0.25rem;
    width: 1rem;
    height: 1rem;
    border-radius: 50%;
    border: 2px solid var(--bg-card);
}

.timeline-content h5 {
    margin: 0 0 0.25rem 0;
    font-size: 1rem;
}

.timeline-content p {
    margin: 0.25rem 0;
}

h4 {
    margin: 1rem 0 0.5rem 0;
    font-size: 0.875rem;
    font-weight: 600;
    color: var(--text-secondary);
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

h4 + p {
    margin-top: 0;
}
</style>

<?php require_once '../includes/footer.php'; ?>
